package sa.roshn.homeservices._shared.notification.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.cloud.tasks.v2.HttpMethod;
import com.google.cloud.tasks.v2.Task;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import sa.roshn.artifacts._share_.infrastructure.RedisClient;
import sa.roshn.artifacts.auth.forgerock.dto.response.UserIdentityResponse;
import sa.roshn.artifacts.auth.forgerock.services.ForgeRockService;
import sa.roshn.artifacts.dynamicconfig.application.FeatureFlagManagerService;
import sa.roshn.artifacts.notification.domain.NotificationPubSubMessage;
import sa.roshn.artifacts.notification.domain.NotificationType;
import sa.roshn.artifacts.order.shopboxo.application.ShopboxoHelperService;
import sa.roshn.artifacts.order.shopboxo.infrastructure.model.GetProductDetailsByIdResponse;
import sa.roshn.artifacts.order.shopboxo.infrastructure.model.GetProductDetailsByIdResponse.ProductCategory;
import sa.roshn.artifacts.payment.domain.AppId;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmLookupFeignClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmSmsTemplateCode;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetLeasesResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetSmsTextRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetSmsTextResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.entity.CrmUnitManagerSmsMapping;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.repository.CrmUnitManagerSmsMappingRepository;
import sa.roshn.homeservices._shared._kernel.infrastructure.platform.client.PlatformNotificationFeignClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.platform.model.PlatformSendSmsRequest;
import sa.roshn.homeservices._shared._kernel.util.CrmConverterUtil;
import sa.roshn.homeservices._shared.cloudtasks.CloudTasksManager;
import sa.roshn.homeservices._shared.dynamicconfig.FlagConstants;
import sa.roshn.homeservices._shared.notification.application.NotificationHelper;
import sa.roshn.homeservices._shared.util.EnvironmentUtil;
import sa.roshn.homeservices._shared.util.InstantUtil;
import sa.roshn.homeservices.servicerequest.constant.ServiceRequestRedisConstant;
import sa.roshn.homeservices.servicerequest.constant.ShopboxoConstant;
import sa.roshn.homeservices.servicerequest.domain.entity.ServiceOrder;
import sa.roshn.homeservices.servicerequest.domain.entity.ServiceOrderItem;
import sa.roshn.homeservices.servicerequest.domain.entity.ServiceRequestEntity;
import sa.roshn.homeservices.servicerequest.domain.model.ScheduledNotification;
import sa.roshn.homeservices.servicerequest.infrastructure.repository.ServiceOrderRepository;
import sa.roshn.homeservices.servicerequest.integration.NotificationPublisherService;
import sa.roshn.homeservices.servicerequest.presentation.request.ServiceRequestNotificationWebhookPayload;
import sa.roshn.homeservices.servicerequest.util.PhoneUtil;
import sa.roshn.homeservices.user.constant.UserPreferenceLanguage;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static sa.roshn.homeservices.servicerequest.domain.entity.ServiceRequestEntity.ServiceRequestStatus;

@Component
@Slf4j
@RequiredArgsConstructor
public class NotificationHelperImpl implements NotificationHelper {

    private final CloudTasksManager cloudTasksManager;
    private final RedisClient redisClient;
    private final ShopboxoHelperService shopboxoHelperService;
    private final FeatureFlagManagerService featureFlagManagerService;
    private final NotificationPublisherService notificationPublisherService;
    private final ForgeRockService forgeRockService;
    private final CrmLookupFeignClient crmLookupFeignClient;
    private final ServiceOrderRepository serviceOrderRepository;
    private final CrmUnitManagerSmsMappingRepository crmUnitManagerSmsMappingRepository;
    private final PlatformNotificationFeignClient platformNotificationFeignClient;

    @Value("${shopboxo.webUrl}")
    private String shopboxoWebUrl;

    @Value("${shopboxo.scheduled-booking-enabled:false}")
    private Boolean shopboxoScheduledBookingEnabled;
    private static final ObjectMapper MAPPER = new ObjectMapper().registerModule(new JavaTimeModule());
    private static final String NOTIFICATION_WEBHOOK_PATH = "/api/v1/service-requests/notification/webhook";

    private static final long APPOINTMENT_REMINDER_MINUTES = 30L;
    private static final long APPOINTMENT_REMINDER_TASK_TTL_DURATION_IN_HOURS = 1L;
    private static final String KSA_TIMEZONE = "GMT+3";
    private static final String NOTIFICATION_DATETIME_FORMAT = "EEE dd. MMM 'at' hh:mm a";
    private static final String BASE_FEEDBACK_REDIRECT_LINK = "roshn-app://home-services/";
    private static final String FEEDBACK_REDIRECT_PATH_FORMAT = "bookings/feedback/%d";
    private static final String SHOPBOXO_ORDER_DETAILS_PATH = "order-details";

    @Override
    public void createReminderNotificationForUser(
            String userId,
            ServiceRequestEntity serviceRequest,
            ServiceOrder serviceOrder
    ) {
        // Toggled by flag
        if (!featureFlagManagerService.isEnabled(FlagConstants.APPOINTMENT_REMINDER_ENABLED)) {
            return;
        }

        String reminderMessage = buildReminderMessage(serviceRequest, serviceOrder);
        Instant reminderInstant = serviceRequest.getAppointmentDate()
                .minus(APPOINTMENT_REMINDER_MINUTES, ChronoUnit.MINUTES); // Minutes before the appointment date
        ScheduledNotification notification = ScheduledNotification.builder()
                .message(reminderMessage)
                .templateCode("MOCKED_FOR_NOW")
                .notificationType(NotificationType.PUSH)
                .seconds(reminderInstant.getEpochSecond())
                .build();
        Task reminderTask = null;
        try {
            reminderTask = scheduleNotificationToUser(userId, serviceRequest.getId(), notification);
        } catch (Exception ex) {
            log.error("Error: Cannot schedule reminder notification for confirmed request: ", ex);
        }

        // Skip persist task ID if create scheduled task failed
        if (reminderTask == null) {
            return;
        }

        String reminderTaskId = CloudTasksManager.getTaskId(reminderTask);
        try {
            Instant reminderTaskLastLive = serviceRequest.getAppointmentDate()
                    .plus(APPOINTMENT_REMINDER_TASK_TTL_DURATION_IN_HOURS, ChronoUnit.HOURS);
            long reminderTtlInSeconds = reminderTaskLastLive.getEpochSecond() - Instant.now().getEpochSecond();
            redisClient.put(
                    ServiceRequestRedisConstant.APPOINTMENT_REMINDER_TASK_PREFIX,
                    serviceRequest.getId().toString(),
                    reminderTaskId,
                    reminderTtlInSeconds,
                    TimeUnit.SECONDS
            );
            log.info("Save a reminder task for Service Request successfully (taskId: {}).", reminderTaskId);
        } catch (Exception ex) {
            log.error("Error: Cannot save reminder task (id: {}) of Service Request (id: {}): ",
                    reminderTaskId, serviceRequest.getId(), ex);
        }
    }

    @Override
    public void deleteServiceRequestReminderNotification(ServiceRequestEntity serviceRequest) {
        String reminderTaskId = null;
        try {
            reminderTaskId = redisClient.get(
                    ServiceRequestRedisConstant.APPOINTMENT_REMINDER_TASK_PREFIX,
                    serviceRequest.getId().toString()
            );
        } catch (Exception ex) {
            log.error("Error: Cannot get service request (id: {}) reminder task id due to: ",
                    serviceRequest.getId(), ex);
        }

        if (Objects.isNull(reminderTaskId)) {
            return;
        }

        try {
            cloudTasksManager.deleteTask(reminderTaskId);
            redisClient.delete(
                    ServiceRequestRedisConstant.APPOINTMENT_REMINDER_TASK_PREFIX,
                    serviceRequest.getId().toString()
            ); // Clean up scheduled reminder task
        } catch (Exception ex) {
            log.error("Error: Cannot delete task (id: {}) in GCP or invalidate in Redis", reminderTaskId, ex);
        }
    }


    private String buildReminderMessage(ServiceRequestEntity serviceRequest, ServiceOrder serviceOrder) {
        ServiceOrderItem orderItem = serviceOrder.getServiceOrderItems().get(0);
        String productId = orderItem.getExternalProductId();
        GetProductDetailsByIdResponse productDetails = shopboxoHelperService.getProductDetailsById(productId);
        ProductCategory productCategory = productDetails.getCategories().get(0);
        String serviceName = String.format(
                "%s - %s",
                productCategory.getTitle(),
                productDetails.getTitle()
        );
        String appointmentDateTimeStr = InstantUtil.toString(
                NOTIFICATION_DATETIME_FORMAT,
                serviceRequest.getAppointmentDate(),
                ZoneId.of(KSA_TIMEZONE) // KSA timezone
        );
        return String.format("You have an upcoming %s appointment %s (%s)",
                serviceName, appointmentDateTimeStr, KSA_TIMEZONE);
    }

    /**
     * Create a scheduled notification task for User
     * @param userId User ID
     * @param serviceRequestId Service Request ID
     * @param notification Scheduled Notification
     * @return Task
     * @throws JsonProcessingException JSON Processing Exception
     */
    private Task scheduleNotificationToUser(
            String userId,
            Long serviceRequestId,
            ScheduledNotification notification
    ) throws JsonProcessingException {
        String taskId = CloudTasksManager.generateTaskId();
        ServiceRequestNotificationWebhookPayload payload = ServiceRequestNotificationWebhookPayload.builder()
                .taskId(taskId)
                .userId(userId)
                .serviceRequestId(serviceRequestId)
                .notificationType(notification.getNotificationType())
                .templateCode(notification.getTemplateCode())
                .message(notification.getMessage())
                .build();
        String encodedPayload = MAPPER.writeValueAsString(payload);
        String notificationWebhookUrl = EnvironmentUtil.getServerPublicUrl() + NOTIFICATION_WEBHOOK_PATH;
        return cloudTasksManager.scheduleHttpTask(
                payload.getTaskId(),
                encodedPayload,
                notificationWebhookUrl,
                HttpMethod.POST,
                notification.getSeconds()
        );
    }

    /**
     * Publish Notification On Request State
     *
     * @param serviceRequest Service Request
     */
    @Override
    public void handlePublishNotificationOnRequestState(
            ServiceRequestEntity serviceRequest,
            ServiceRequestStatus status
    ) {
        String userLanguage = getUserPreferenceLanguage(serviceRequest.getForgeRockUserId());
        switch (status) {
            case COMPLETED -> {
                // TODO: SMS templates moved to Notification Services - implement new integration
                // GetSmsTextRequest smsTextRequest = GetSmsTextRequest.builder()
                //         .templateCode(CrmSmsTemplateCode.HOME_SERVICE_COMPLETED.getValue())
                //         .language(userLanguage)
                //         .build();
                // GetSmsTextResponse smsTextResponse = crmLookupFeignClient.getSmsText(smsTextRequest);
                Map<String, String> props = Map.of(
                        "url", buildRedirectFeedbackLink(serviceRequest)
                );
                String defaultMessage = "Your home service request has been completed. Thank you for using our services.";
                log.info("Sending notification for forgerock id : {} with default message for request status Complete",
                        serviceRequest.getForgeRockUserId());
                this.publishServiceRequestNotification(serviceRequest, defaultMessage, props);
            }
            case IN_PROGRESS -> {
                ServiceOrder primaryOrder = getRequestPrimaryOrder(serviceRequest);
                ServiceOrderItem orderItem = primaryOrder.getServiceOrderItems().get(0);

                GetProductDetailsByIdResponse productDetails = shopboxoHelperService
                        .getProductDetailsById(orderItem.getExternalProductId(), userLanguage);
                String homeServiceName = getHomeServiceName(productDetails);

                String categorySlug = productDetails.getCategories().get(0).getSlug();
                GetSmsTextRequest smsTextRequest;
                if (Boolean.FALSE.equals(shopboxoScheduledBookingEnabled)
                        || categorySlug.equals(ShopboxoConstant.SUBMIT_GENERAL_REQUEST_CATEGORY_SLUG)
                ) {
                    smsTextRequest = GetSmsTextRequest.builder()
                            .templateCode(CrmSmsTemplateCode.HOME_SERVICE_REQUEST_CONFIRMED.getValue())
                            .language(userLanguage)
                            .homeMaintenanceService(homeServiceName)
                            .build();
                } else {
                    String appointmentDateStr = InstantUtil.toString(
                            InstantUtil.DATE_FORMAT,
                            serviceRequest.getAppointmentDate(),
                            ZoneId.of(KSA_TIMEZONE)
                    );
                    String appointmentTimeStr = InstantUtil.toString(
                            InstantUtil.TIME_FORMAT,
                            serviceRequest.getAppointmentDate(),
                            ZoneId.of(KSA_TIMEZONE)
                    );
                    smsTextRequest = GetSmsTextRequest.builder()
                            .templateCode(CrmSmsTemplateCode.HOME_SERVICE_REQUEST_BOOKED.getValue())
                            .language(userLanguage)
                            .homeMaintenanceService(homeServiceName)
                            .homeServiceDate(appointmentDateStr)
                            .homeServiceTime(appointmentTimeStr)
                            .build();
                }
                GetSmsTextResponse smsTextResponse = crmLookupFeignClient.getSmsText(smsTextRequest);
                log.info("Sending notification for forgerock id : {} and text : \"{}\" for request status In progress",
                        serviceRequest.getForgeRockUserId(), smsTextResponse.getSms());
                this.publishServiceRequestNotification(serviceRequest, smsTextResponse.getSms(), null);
            }
            default -> log.warn("Unsupported status ({}) on handling notification publish.", status.name());
        }
    }

    @Override
    public void sendRequestConfirmationSmsToUnitManager(ServiceRequestEntity serviceRequest, String confirmedSrNumber) {
        // Feature flag
        if (!featureFlagManagerService.isEnabled(FlagConstants.MANAGER_SMS_ENABLED)) {
            log.warn("WARN! Skip sending message due to MANAGER_SMS_ENABLED flag is disabled.");
            return;
        }

        // Config-based flag
        String activeProfile = EnvironmentUtil.getActiveProfile();
        if (activeProfile.equals("local")) {
            log.warn("WARN! Skip sending message to unit manager for local environment.");
            return;
        }

        List<CrmUnitManagerSmsMapping> smsMappingList = crmUnitManagerSmsMappingRepository
                .findByUnitCode(serviceRequest.getUnitCode());
        if (smsMappingList.isEmpty()) {
            log.error("Error! Cannot send sms to unit code (ServiceRequest ID: {}) manager due to unknown mapping",
                    serviceRequest.getId());
            return;
        }

        // Get service request details
        ServiceOrder primaryOrder = getRequestPrimaryOrder(serviceRequest);
        ServiceOrderItem orderItem = primaryOrder.getServiceOrderItems().get(0);
        GetProductDetailsByIdResponse productDetails = shopboxoHelperService
                .getProductDetailsById(orderItem.getExternalProductId());
        String homeServiceName = getHomeServiceName(productDetails);

        // Get service request confirmed time
        String requestRaisedDateTimeStr = InstantUtil.toString(
                InstantUtil.DATE_TIME_FORMAT,
                serviceRequest.getCreatedAt(),
                ZoneId.of(KSA_TIMEZONE)
        );

        // Get shopboxo order details link to view order on Seller Dashboard
        String shopboxoOrderLink = String.format(
                "%s/%s/%s",
                shopboxoWebUrl,
                SHOPBOXO_ORDER_DETAILS_PATH,
                primaryOrder.getExternalStoreOrderId()
        );

        // Iterate & Send SMS to all phones of the unit's manager
        for (var smsMapping: smsMappingList) {
            sendRequestConfirmationSmsByMapping(
                    serviceRequest,
                    smsMapping,
                    homeServiceName,
                    requestRaisedDateTimeStr,
                    confirmedSrNumber,
                    shopboxoOrderLink
            );
        }
    }

    @Override
    public void sendNotificationToTenant(GetLeasesResponse.LeaseRegistration leaseRegistration) {
        String userLanguage = UserPreferenceLanguage.ARABIC.getValue();
        GetSmsTextRequest smsTextRequest = GetSmsTextRequest.builder()
                .templateCode(CrmSmsTemplateCode.NEW_TENANT_WELCOME_MESSAGE.getValue())
                .language(userLanguage)
                .customerFirstName(leaseRegistration.getTenantName())
                .community(leaseRegistration.getCommunity())
                .unitCode(leaseRegistration.getUnitCode())
                .build();
        GetSmsTextResponse smsTextResponse;
        try {
            smsTextResponse = crmLookupFeignClient.getSmsText(smsTextRequest);
            sendSmsByPlatform(smsTextResponse,
                    CrmConverterUtil.phoneNumberParser(leaseRegistration.getTenantPhoneNumber()));
        } catch (Exception ex) {
            log.error("Failed to send SMS to {} with error {}",
                    PhoneUtil.getMaskedPhoneNumber(leaseRegistration.getTenantPhoneNumber(), 3), ex.getMessage());
        }
    }

    @Override
    public void sendNotificationToCommunityTeam(GetLeasesResponse.LeaseRegistration leaseRegistration) {
        String userLanguage = UserPreferenceLanguage.ARABIC.getValue();
        GetSmsTextRequest smsTextRequest = GetSmsTextRequest.builder()
                .templateCode(CrmSmsTemplateCode.NEW_TENANT_REACHED.getValue())
                .language(userLanguage)
                .customerFirstName(leaseRegistration.getTenantName())
                .community(leaseRegistration.getCommunity())
                .unitCode(leaseRegistration.getUnitCode())
                .customerPhone(leaseRegistration.getTenantPhoneNumber()) // change this to community team phone
                .build();
        GetSmsTextResponse smsTextResponse;
        try {
            smsTextResponse = crmLookupFeignClient.getSmsText(smsTextRequest);
            // change this to community team phone
            sendSmsByPlatform(smsTextResponse,
                    CrmConverterUtil.phoneNumberParser(leaseRegistration.getTenantPhoneNumber()));
        } catch (Exception ex) {
            log.error("Failed to send SMS to {} with error {}",
                    PhoneUtil.getMaskedPhoneNumber(leaseRegistration.getTenantPhoneNumber(), 3), ex.getMessage());
        }

    }

    @Override
    public void sendNotificationToHomeOwner(GetLeasesResponse.LeaseRegistration leaseRegistration) {
        String userLanguage = UserPreferenceLanguage.ARABIC.getValue();
        GetSmsTextRequest smsTextRequest = GetSmsTextRequest.builder()
                .templateCode(CrmSmsTemplateCode.NEW_TENANT_ADDITION_CONFIRMATION.getValue())
                .language(userLanguage)
                .customerFirstName(leaseRegistration.getTenantName())
                .customerPhone(leaseRegistration.getTenantPhoneNumber())
                .unitCode(leaseRegistration.getUnitCode())
                .build();
        GetSmsTextResponse smsTextResponse;
        try {
            smsTextResponse = crmLookupFeignClient.getSmsText(smsTextRequest);
            sendSmsByPlatform(smsTextResponse,
                    CrmConverterUtil.phoneNumberParser(leaseRegistration.getLandLordPhoneNumber()));
        } catch (Exception ex) {
            log.error("Failed to send SMS to {} with error {}",
                    PhoneUtil.getMaskedPhoneNumber(leaseRegistration.getTenantPhoneNumber(), 3), ex.getMessage());
        }

    }

    private void sendSmsByPlatform(GetSmsTextResponse message, String phone) {

        try {
            PlatformSendSmsRequest sendSmsRequest = PlatformSendSmsRequest.builder()
                    .phoneNumber(phone)
                    .message(message.getSms())
                    .build();
            platformNotificationFeignClient.sendSms(sendSmsRequest);
        } catch (Exception ex) {
            throw ex;
        }

    }
    /**
     * Send request confirmation SMS by mapping.
     * @param serviceRequest Service Request
     * @param smsMapping SMS Mapping
     * @param serviceName Service Name (Sub-category)
     * @param requestTimeStr Request raised time string
     * @param srNumber Confirmed SR Number
     * @param shopboxoOrderLink Shopboxo Order Details Link
     */
    private void sendRequestConfirmationSmsByMapping(
            ServiceRequestEntity serviceRequest,
            CrmUnitManagerSmsMapping smsMapping,
            String serviceName,
            String requestTimeStr,
            String srNumber,
            String shopboxoOrderLink
    ) {
        String smsMsg = String.format(
                "Dear %s, %s complaint raised by %s at %s, with "
                        + "service request ID %s. Please check any further details here: %s",
                smsMapping.getManagerName(),
                serviceName,
                smsMapping.getUnitCode(),
                requestTimeStr,
                srNumber,
                shopboxoOrderLink
        );
        String managerFullPhoneNo = String.format(
                "%s%s",
                smsMapping.getPhoneCountryCode(),
                smsMapping.getManagerPhoneNo()
        );
        String maskedPhoneNumber = PhoneUtil.getMaskedPhoneNumber(managerFullPhoneNo, 3); // Unmask last 3 digits
        try {
            PlatformSendSmsRequest sendSmsRequest = PlatformSendSmsRequest.builder()
                    .phoneNumber(managerFullPhoneNo)
                    .message(smsMsg)
                    .build();
            platformNotificationFeignClient.sendSms(sendSmsRequest);
            log.info(
                    "Send SMS to manager of unit (code: {}) to phone (No: {}) successfully.",
                    smsMapping.getUnitCode(),
                    maskedPhoneNumber
            );
        } catch (Exception ex) {
            log.error(
                    "Error while sending service request (ID: {}) confirmation SMS to unit manager phone (No: {}):",
                    serviceRequest.getId(),
                    maskedPhoneNumber,
                    ex
            );
        }
    }

    /**
     * Get primary order of given service request.
     * @param serviceRequest Service Request
     * @return Primary Service Order
     */
    private ServiceOrder getRequestPrimaryOrder(ServiceRequestEntity serviceRequest) {
        List<ServiceOrder> orders = serviceOrderRepository
                .findWithItemsByServiceRequestId(serviceRequest.getId());
        return orders.get(0);
    }

    private String buildRedirectFeedbackLink(ServiceRequestEntity serviceRequest) {
        String feedbackRedirectPath = String.format(FEEDBACK_REDIRECT_PATH_FORMAT, serviceRequest.getId());
        String encodedPath = URLEncoder.encode(
                feedbackRedirectPath,
                StandardCharsets.UTF_8
        );
        return BASE_FEEDBACK_REDIRECT_LINK + encodedPath;
    }

    private void publishServiceRequestNotification(
            ServiceRequestEntity serviceRequest,
            String textMessage,
            Map<String, String> props
    ) {
        String userId = Objects.requireNonNull(serviceRequest.getForgeRockUserId());
        try {
            var notificationMessage = NotificationPubSubMessage.builder()
                    .id(UUID.randomUUID())
                    .appId(AppId.HOME_SERVICES)
                    .type(NotificationType.PUSH)
                    .userId(userId)
                    .recipient(userId)
                    .message(textMessage)
                    .createdAt(Instant.now())
                    .build();
            if (Objects.nonNull(props)) {
                notificationMessage.setProps(props);
            }
            var orderingKey = notificationPublisherService.buildOrderingKey(
                    userId,
                    NotificationType.PUSH
            );
            notificationPublisherService.publishMessage(orderingKey, notificationMessage);
        } catch (Exception ex) {
            log.error(
                    "Error while publishing notification message for request id {}",
                    serviceRequest.getId(),
                    ex
            );
        }
    }

    private String getHomeServiceName(GetProductDetailsByIdResponse productDetails) {
        GetProductDetailsByIdResponse.ProductCategory productCategory = productDetails.getCategories().get(0);
        return String.format(
                "%s - %s",
                productCategory.getTitle(),
                productDetails.getTitle()
        );
    }

    private String getUserPreferenceLanguage(String userId) {
        Optional<UserIdentityResponse> optionalUser = forgeRockService.getUserByForgeRockId(userId);
        if (optionalUser.isEmpty()) {
            return UserPreferenceLanguage.ARABIC.getValue();
        }
        var user = optionalUser.get();
        if (Objects.isNull(user.getPreferences())) {
            return UserPreferenceLanguage.ARABIC.getValue();
        }
        return user.getPreferences().getLanguage().toUpperCase();
    }
}
