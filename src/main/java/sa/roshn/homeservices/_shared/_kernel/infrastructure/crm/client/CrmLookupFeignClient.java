package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetCommunitiesResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetProductsResponse;

@FeignClient(
        name = "CrmLookupFeignClient",
        url = "${crm.ent.api.url}",
        configuration = CrmFeignConfig.class
)
public interface CrmLookupFeignClient {

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/products")
    GetProductsResponse getProducts(@RequestHeader("Authorization") String bearerToken,
                                    @RequestParam("CommunityName") String communityName,
                                    @RequestParam("ProjectName") String projectName,
                                    @RequestParam("NeighbourhoodName") String neighbourhoodName,
                                    @RequestParam("UnitCode") String unitCode);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/property-master/communities")
    GetCommunitiesResponse getCommunities(@RequestHeader("Authorization") String bearerToken);
}
