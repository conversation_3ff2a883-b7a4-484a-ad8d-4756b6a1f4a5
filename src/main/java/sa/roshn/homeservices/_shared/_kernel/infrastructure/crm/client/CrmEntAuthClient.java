package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmEntFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmApiTokenResponse;

@FeignClient(
        name = "CrmEntAuthClient",
        url = "${crm.ent.api.url}",
        configuration = CrmEntFeignConfig.class
)
public interface CrmEntAuthClient {

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/oauth/generate?grant_type=client_credentials")
    CrmApiTokenResponse getToken();
}
