package sa.roshn.homeservices._shared._kernel.infrastructure.crm.config;

import feign.RequestInterceptor;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
public class CrmFeignConfig {
    private final CrmBearerTokenInterceptor crmBearerTokenInterceptor;

    @Bean
    public Encoder multipartFormEncoder() {
        return new SpringFormEncoder(
                new SpringEncoder(() -> new HttpMessageConverters(new RestTemplate().getMessageConverters()))
        );
    }

    /**
     * Enable this bean for adding Bearer Authorization header
     * for e.g. Authorization: Bearer <token>
     */
    @Bean
    public RequestInterceptor bearerTokenRequestInterceptor() {
        return crmBearerTokenInterceptor;
    }
}
