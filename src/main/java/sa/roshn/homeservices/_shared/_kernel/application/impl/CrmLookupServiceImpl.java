package sa.roshn.homeservices._shared._kernel.application.impl;

import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sa.roshn.artifacts._share_.infrastructure.CacheClient;
import sa.roshn.artifacts._share_.log.RoshnLogger;
import sa.roshn.artifacts._share_.log.RoshnModule;
import sa.roshn.homeservices._shared._kernel.application.CrmLookupService;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmEntAuthClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmEntLookupFeignClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmLookupFeignClient;
import sa.roshn.homeservices._shared.log.ErrorSource;
import sa.roshn.homeservices._shared.log.SystemErrorCode;
import sa.roshn.homeservices._shared.log.SystemType;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetCommunitiesResponse.CommunityInfo;
import static sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetProductsResponse.ProductInfo;

@Service
@Slf4j
@RequiredArgsConstructor
@RoshnModule(properties = {
    @RoshnModule.Property(key = "name", value = "crm")
})
public class CrmLookupServiceImpl implements CrmLookupService {
    private final CrmLookupFeignClient crmLookupFeignClient;
    private final CrmEntLookupFeignClient crmEntLookupFeignClient;
    private final CrmEntAuthClient crmEntAuthClient;
    private final CacheClient cacheClient;
    private static final String LOOKUP_CACHE_NAME = "lookup";
    private static final String COMMUNITY_CACHE_KEY = "communities";
    private static final long COMMUNITIES_CACHE_TTL_IN_HOURS = 20L;
    private static final String CRM_ENT_CACHE_KEY = "crm-ent-auth";
    private static final long CRM_ENT_CACHE_TTL_IN_HOURS = 1L;

    @Override
    public List<CommunityInfo> getCommunites() {
        List<CommunityInfo> cachedCommunities = null;

        try {
            cachedCommunities = cacheClient.get(LOOKUP_CACHE_NAME, COMMUNITY_CACHE_KEY);
        } catch (Exception ex) {
            log.error("Cannot get cache for communities: ", ex);
        }

        if (cachedCommunities != null) {
            return cachedCommunities;
        }

        var response = crmLookupFeignClient.getCommunities();

        try {
            cacheClient.put(
                    LOOKUP_CACHE_NAME,
                    COMMUNITY_CACHE_KEY,
                    response.getCommunities(),
                    COMMUNITIES_CACHE_TTL_IN_HOURS,
                    TimeUnit.HOURS
            );
        } catch (Exception ex) {
            log.error("Cannot put cache for communities: ", ex);
        }
        return response.getCommunities();
    }

    @Override
    public ProductInfo getProductInfoByUnitCode(String unitCode) {
        String token = getTokenFromCacheOrGenerate();

        try {
            var response = crmEntLookupFeignClient.getProducts(token, unitCode);
            return response.getProducts().get(0);
        } catch (FeignException.Unauthorized ex) {
            log.info("Token expired or invalid, refreshing token and retrying...");
            token = refreshAndCacheToken();
            var response = crmEntLookupFeignClient.getProducts(token, unitCode);
            List<ProductInfo> products = response.getProducts();
            return products != null && !products.isEmpty() ? products.get(0) : null;

        }
    }

    private String getTokenFromCacheOrGenerate() {
        try {
            return cacheClient.get(LOOKUP_CACHE_NAME, CRM_ENT_CACHE_KEY);
        } catch (Exception e) {
            log.info("Cannot get CRM auth token from cache");
            return refreshAndCacheToken();
        }
    }

    private String refreshAndCacheToken() {
        String newToken = null;
        try {
            newToken = "Bearer " + crmEntAuthClient.getToken().getAccessToken();
            cacheClient.put(
                    LOOKUP_CACHE_NAME,
                    CRM_ENT_CACHE_KEY,
                    newToken,
                    CRM_ENT_CACHE_TTL_IN_HOURS,
                    TimeUnit.HOURS
            );
        } catch (Exception e) {
            RoshnLogger.error(
                SystemErrorCode.CRM_REQUEST_ERROR, ErrorSource.CRM, SystemType.EXTERNAL.getName(),
                "Error while authenticating from CRM ENT to get the token", e
            );
        }
        return newToken;
    }

}
