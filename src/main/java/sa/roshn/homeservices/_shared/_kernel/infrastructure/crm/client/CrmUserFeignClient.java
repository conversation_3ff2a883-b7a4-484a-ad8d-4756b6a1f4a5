package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CheckContactExistsCrmRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CheckContactExistsCrmResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateContactRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateContactResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateHouseHoldMemberRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateHouseHoldMemberResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateLeaseRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateLeaseResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateTenantRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateTenantResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetContactCrmResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetHomeOwnerResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetHouseHoldMembersResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetLeasesResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetTenantResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetUnitHandoverRequestResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.UpdateHouseHoldMemberRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.UpdateHouseHoldMemberResponse;

import java.util.Map;

@FeignClient(
    name = "CrmUserFeignClient",
    url = "${crm.ent.api.url}",
    configuration = CrmFeignConfig.class
)
public interface CrmUserFeignClient {

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/homeOwners")
    GetHomeOwnerResponse getHomeOwnerByRegistryId(@RequestHeader("Authorization") String bearerToken,
                                                  @RequestParam("RegistryId") String registryId);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/homeOwners")
    GetHomeOwnerResponse getAllHomeOwners(@RequestHeader("Authorization") String bearerToken);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/tenants")
    GetTenantResponse getTenantInfo(@RequestHeader("Authorization") String bearerToken,
                                    @RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/tenants")
    CreateTenantResponse createTenant(@RequestHeader("Authorization") String bearerToken,
                                      CreateTenantRequest createTenantRequest);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/leaseRegistrations")
    GetLeasesResponse getLeases(@RequestHeader("Authorization") String bearerToken,
                                @RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/leaseRegistrations")
    CreateLeaseResponse createLease(@RequestHeader("Authorization") String bearerToken,
                                    CreateLeaseRequest createLeaseRequest);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/unitHandoverRequests")
    GetUnitHandoverRequestResponse getUnitHandoverRequest(@RequestHeader("Authorization") String bearerToken,
                                                          @RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/contacts/exists")
    CheckContactExistsCrmResponse checkContactExists(@RequestHeader("Authorization") String bearerToken,
                                                     CheckContactExistsCrmRequest crmRequest);

    // Household Members API
    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/houseHoldMembers")
    GetHouseHoldMembersResponse getHouseHoldMembers(@RequestHeader("Authorization") String bearerToken,
                                                    @RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/houseHoldMembers")
    CreateHouseHoldMemberResponse addHouseHoldMember(@RequestHeader("Authorization") String bearerToken,
                                                     CreateHouseHoldMemberRequest createHouseHoldMemberRequest);

    @GCPLogger(logResponse = true)
    @PatchMapping(value = "/v1/crm/houseHoldMembers")
    UpdateHouseHoldMemberResponse updateHouseHoldMember(@RequestHeader("Authorization") String bearerToken,
                                                        UpdateHouseHoldMemberRequest updateHouseHoldMemberRequest,
                                                        @RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/contacts/{contactRegistryId}")
    GetContactCrmResponse getContact(@RequestHeader("Authorization") String bearerToken,
                                     @PathVariable String contactRegistryId);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/contacts")
    CreateContactResponse createContact(@RequestHeader("Authorization") String bearerToken,
                                        CreateContactRequest createContact);
}
