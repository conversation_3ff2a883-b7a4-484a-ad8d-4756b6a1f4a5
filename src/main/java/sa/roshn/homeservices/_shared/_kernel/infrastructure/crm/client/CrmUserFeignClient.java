package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CheckContactExistsCrmRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CheckContactExistsCrmResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateContactRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateContactResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateHouseHoldMemberRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateHouseHoldMemberResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateLeaseRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateLeaseResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateTenantRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateTenantResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetContactCrmResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetHomeOwnerResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetHouseHoldMembersResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetLeasesResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetTenantResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetUnitHandoverRequestResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.UpdateHouseHoldMemberRequest;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.UpdateHouseHoldMemberResponse;

import java.util.Map;

@FeignClient(
    name = "CrmUserFeignClient",
    url = "${crm.ent.api.url}",
    configuration = CrmFeignConfig.class
)
public interface CrmUserFeignClient {

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/homeOwners")
    GetHomeOwnerResponse getHomeOwnerByRegistryId(@RequestParam("RegistryId") String registryId);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/homeOwners")
    GetHomeOwnerResponse getAllHomeOwners();

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/tenants")
    GetTenantResponse getTenantInfo(@RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/tenants")
    CreateTenantResponse createTenant(CreateTenantRequest createTenantRequest);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/leaseRegistrations")
    GetLeasesResponse getLeases(@RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/leaseRegistrations")
    CreateLeaseResponse createLease(CreateLeaseRequest createLeaseRequest);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/unitHandoverRequests")
    GetUnitHandoverRequestResponse getUnitHandoverRequest(@RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/contacts/exists")
    CheckContactExistsCrmResponse checkContactExists(CheckContactExistsCrmRequest crmRequest);

    // Household Members API
    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/houseHoldMembers")
    GetHouseHoldMembersResponse getHouseHoldMembers(@RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/houseHoldMembers")
    CreateHouseHoldMemberResponse addHouseHoldMember(CreateHouseHoldMemberRequest createHouseHoldMemberRequest);

    @GCPLogger(logResponse = true)
    @PatchMapping(value = "/v1/crm/houseHoldMembers")
    UpdateHouseHoldMemberResponse updateHouseHoldMember(UpdateHouseHoldMemberRequest updateHouseHoldMemberRequest,
                                                        @RequestParam Map<String, String> param);

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/contacts/{contactRegistryId}")
    GetContactCrmResponse getContact(@PathVariable String contactRegistryId);

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/contacts")
    CreateContactResponse createContact(CreateContactRequest createContact);
}
