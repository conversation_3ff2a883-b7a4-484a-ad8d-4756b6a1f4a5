package sa.roshn.homeservices._shared._kernel.infrastructure.crm.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmEntAuthClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmApiTokenResponse;

@Component
@RequiredArgsConstructor
@Slf4j
public class CrmBearerTokenInterceptor implements RequestInterceptor {
    
    private final CrmEntAuthClient crmEntAuthClient;
    private volatile String cachedToken;
    private volatile long tokenExpiryTime;
    
    @Override
    public void apply(RequestTemplate template) {
        String token = getValidToken();
        if (token != null) {
            template.header("Authorization", "Bearer " + token);
        }
    }
    
    private String getValidToken() {
        try {
            // Check if we have a cached token that's still valid (with 5 minute buffer)
            if (cachedToken != null && System.currentTimeMillis() < (tokenExpiryTime - 300000)) {
                return cachedToken;
            }
            
            // Get new token
            log.debug("Fetching new CRM authentication token");
            CrmApiTokenResponse tokenResponse = crmEntAuthClient.getToken();
            
            if (tokenResponse != null && tokenResponse.getAccessToken() != null) {
                cachedToken = tokenResponse.getAccessToken();
                // Set expiry time (assuming token expires in 1 hour if not specified)
                int expiresIn = tokenResponse.getExpiresIn() > 0 ? tokenResponse.getExpiresIn() : 3600;
                tokenExpiryTime = System.currentTimeMillis() + (expiresIn * 1000L);
                
                log.debug("Successfully obtained CRM authentication token");
                return cachedToken;
            }
            
            log.warn("Failed to obtain CRM authentication token");
            return null;
            
        } catch (Exception e) {
            log.error("Error obtaining CRM authentication token", e);
            return null;
        }
    }
}
