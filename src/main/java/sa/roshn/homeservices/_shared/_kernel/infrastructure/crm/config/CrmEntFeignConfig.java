package sa.roshn.homeservices._shared._kernel.infrastructure.crm.config;

import feign.auth.BasicAuthRequestInterceptor;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

@RequiredArgsConstructor
public class CrmEntFeignConfig {
    private final CrmEntApiCred crmEntApiCred;

    @Bean
    public Encoder multipartFormEncoder() {
        return new SpringFormEncoder(
                new SpringEncoder(() -> new HttpMessageConverters(new RestTemplate().getMessageConverters()))
        );
    }

    @Bean
    public BasicAuthRequestInterceptor basicAuthRequestInterceptor() {
        return new BasicAuthRequestInterceptor(
                crmEntApiCred.getUsername(),
                crmEntApiCred.getPassword()
        );
    }

}
