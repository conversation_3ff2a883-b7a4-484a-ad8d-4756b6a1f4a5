package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmPushEventResponse;

import java.util.List;

@FeignClient(
        name = "CrmEventFeignClient",
        url = "${crm.ent.api.url}",
        configuration = CrmFeignConfig.class
)
public interface CrmEventFeignClient {
    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/eventsService/events")
    Response pullServiceRequestEvent(
            @RequestParam("SourceSystem") String sourceSystem,
            @RequestParam("EventId") String eventId,
            @RequestParam("EventTimestamp") String eventTimestamp, // Z timestamp
            @RequestParam("ObjectName") String objectName,
            @RequestParam("RecordIdentifier") String recordIdentifier,
            @RequestParam("pullingSystem") String pullingSystem
    );

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/eventsService/events", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    CrmPushEventResponse pushServiceRequestEvent(
            @RequestHeader("Authorization") String bearerToken,
            @RequestPart("EventType") String eventType,
            @RequestPart("SourceSystem") String sourceSystem,
            @RequestPart("ObjectName") String objectName,
            @RequestPart("RecordIdentifier") String recordIdentifier,
            @RequestPart("EventTimestamp") String eventTimestamp,
            @RequestPart("EventData") String eventData
    );

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/eventsService/events", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CrmPushEventResponse pushServiceRequestEvent(
            @RequestHeader("Authorization") String bearerToken,
            @RequestPart("EventType") String eventType,
            @RequestPart("SourceSystem") String sourceSystem,
            @RequestPart("ObjectName") String objectName,
            @RequestPart("RecordIdentifier") String recordIdentifier,
            @RequestPart("EventTimestamp") String eventTimestamp,
            @RequestPart("EventData") String eventData,
            @RequestPart("file") List<MultipartFile> attachment
    );

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/eventsService/events", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CrmPushEventResponse pushServiceRequestEvent(
            @RequestHeader("Authorization") String bearerToken,
            @RequestPart("EventType") String eventType,
            @RequestPart("SourceSystem") String sourceSystem,
            @RequestPart("ObjectName") String objectName,
            @RequestPart("RecordIdentifier") String recordIdentifier,
            @RequestPart("EventTimestamp") String eventTimestamp,
            @RequestPart("file") List<MultipartFile> attachment
    );
}
