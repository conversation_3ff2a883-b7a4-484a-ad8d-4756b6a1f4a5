package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import feign.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmPushEventResponse;

import java.util.List;

@FeignClient(
        name = "CrmEventFeignClient",
        url = "${crm.ent.api.url}",
        configuration = CrmFeignConfig.class
)
public interface CrmEventFeignClient {
    @GCPLogger(logResponse = true)
    @GetMapping(value = "/EVENTS_SERVICE/1.0/pullEvent?EventId={eventId}"
            + "&EventTimestamp={eventTimestamp}"
            + "&SourceSystem={sourceSystem}"
            + "&ObjectName=ServiceRequest"
            + "&RecordIdentifier={recordIdentifier}")
    Response pullServiceRequestEvent(
            @PathVariable("sourceSystem") String sourceSystem,
            @PathVariable("eventId") String eventId,
            @PathVariable("eventTimestamp") String eventTimestamp, // Z timestamp
            @PathVariable("recordIdentifier") String recordIdentifier
    );

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/EVENTS_SERVICE/1.0/pushEvent", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    CrmPushEventResponse pushServiceRequestEvent(
            @RequestPart("EventType") String eventType,
            @RequestPart("SourceSystem") String sourceSystem,
            @RequestPart("ObjectName") String objectName,
            @RequestPart("RecordIdentifier") String recordIdentifier,
            @RequestPart("EventTimestamp") String eventTimestamp,
            @RequestPart("EventData") String eventData
    );

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/EVENTS_SERVICE/1.0/pushEvent", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CrmPushEventResponse pushServiceRequestEvent(
            @RequestPart("EventType") String eventType,
            @RequestPart("SourceSystem") String sourceSystem,
            @RequestPart("ObjectName") String objectName,
            @RequestPart("RecordIdentifier") String recordIdentifier,
            @RequestPart("EventTimestamp") String eventTimestamp,
            @RequestPart("EventData") String eventData,
            @RequestPart("file") List<MultipartFile> attachment
    );

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/EVENTS_SERVICE/1.0/pushEvent", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CrmPushEventResponse pushServiceRequestEvent(
            @RequestPart("EventType") String eventType,
            @RequestPart("SourceSystem") String sourceSystem,
            @RequestPart("ObjectName") String objectName,
            @RequestPart("RecordIdentifier") String recordIdentifier,
            @RequestPart("EventTimestamp") String eventTimestamp,
            @RequestPart("file") List<MultipartFile> attachment
    );
}
