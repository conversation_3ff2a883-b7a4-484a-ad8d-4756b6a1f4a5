package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetProductsResponse;

@FeignClient(
        name = "CrmEntLookupFeignClient",
        url = "${crm.ent.api.url}"
)
public interface CrmEntLookupFeignClient {

    @GCPLogger(logResponse = true)
    @GetMapping(value = "/v1/crm/products?UnitCode={unitCode}")
    GetProductsResponse getProducts(@RequestHeader("Authorization") String bearerToken,
                                    @PathVariable("unitCode") String unitCode);
}
