package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.UploadAttachmentCrmResponse;

import java.util.Map;


@FeignClient(
        name = "CrmGlobalFeignClient",
        url = "${crm.ent.api.url}",
        configuration = CrmFeignConfig.class
)
public interface CrmGlobalFeignClient {

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/v1/crm/attachments",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    UploadAttachmentCrmResponse uploadAttachment(Map<String, ?> requestBody);


}
