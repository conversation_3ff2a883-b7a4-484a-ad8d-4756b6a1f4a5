package sa.roshn.homeservices._shared._kernel.infrastructure.crm.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import sa.roshn.artifacts._share_.analytic.annotation.GCPLogger;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.config.CrmFeignConfig;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.UploadAttachmentCrmResponse;

import java.util.Map;


@FeignClient(
        name = "CrmGlobalFeignClient",
        url = "${crm.ent.api.url}",
        configuration = CrmFeignConfig.class
)
public interface CrmGlobalFeignClient {

    @GCPLogger(logResponse = true)
    @PostMapping(value = "/CRM_UPLOAD_ATTACHMENTS/1.0/submitAttachment",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    UploadAttachmentCrmResponse uploadAttachment(Map<String, ?> requestBody);


}
