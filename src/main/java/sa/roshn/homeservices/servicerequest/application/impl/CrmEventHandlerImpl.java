package sa.roshn.homeservices.servicerequest.application.impl;

import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import sa.roshn.artifacts._share_.log.RoshnModule;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmEventFeignClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmEventData;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmPullEvent;
import sa.roshn.homeservices._shared._kernel.util.CrmConverterUtil;
import sa.roshn.homeservices._shared.http.error.CommonHttpError;
import sa.roshn.homeservices._shared.http.error.RoshnHttpException;
import sa.roshn.homeservices._shared.notification.application.NotificationHelper;
import sa.roshn.homeservices._shared.util.InstantUtil;
import sa.roshn.homeservices._shared.util.MultipartFormDataParser;
import sa.roshn.homeservices.servicerequest.application.CrmEventHandler;
import sa.roshn.homeservices.servicerequest.domain.error.ServiceRequestHttpError;
import sa.roshn.homeservices.servicerequest.infrastructure.repository.ServiceRequestRepository;
import sa.roshn.homeservices.servicerequest.presentation.request.CrmEventPayload;
import sa.roshn.homeservices.servicerequest.util.CrmEventUtil;
import sa.roshn.homeservices.servicerequest.util.ServiceRequestActionLogHelper;

import java.util.List;
import java.util.Objects;

import static sa.roshn.homeservices.servicerequest.domain.entity.ServiceRequestEntity.ServiceRequestStatus;


@Service
@RequiredArgsConstructor
@Slf4j
@RoshnModule(properties = {
    @RoshnModule.Property(key = "name", value = "service-request")
})
public class CrmEventHandlerImpl implements CrmEventHandler {

    private final CrmEventFeignClient crmEventFeignClient;
    private final ServiceRequestRepository serviceRequestRepository;
    private final NotificationHelper notificationHelper;
    private final ServiceRequestActionLogHelper serviceRequestActionLogHelper;


    @Override
    public void handleServiceRequestStatusUpdate(CrmEventPayload event) {
        log.info("Handling CRM callback for service request (id: {}, timestamp: {}, "
            + "recordIdentifier: {})", event.getEventId(), event.getEventTimestamp(), event.getRecordIdentifier());

        var serviceRequest = serviceRequestRepository.findByCrmRecordIdentifier(event.getRecordIdentifier())
                .orElseThrow(() -> new RoshnHttpException(ServiceRequestHttpError.SERVICE_REQUEST_NOT_FOUND));

        String eventTimestampStr = InstantUtil.toZTimestampString(event.getEventTimestamp());
        Response eventPayload = crmEventFeignClient.pullServiceRequestEvent(
                event.getSourceSystem(),
                event.getEventId(),
                eventTimestampStr,
                "ServiceRequest",
                event.getRecordIdentifier(),
                "HOME_SERVICES"
        );

        List<MultipartFormDataParser.MultipartFormData> formData = MultipartFormDataParser.parse(eventPayload);
        CrmPullEvent pullEvent;
        try {
            pullEvent = CrmConverterUtil.convertPullEventFormDataToModel(formData);
        } catch (Exception ex) {
            log.error("Cannot convert pull event from data to model: ", ex);
            throw new RoshnHttpException(CommonHttpError.INTERNAL_SERVER_ERROR);
        }
        CrmEventData eventData = pullEvent.getEventData();

        log.info("Event data from CRM callback for service request id : {} is {}",
                serviceRequest.getId(), eventData);

        // Handle status mapping
        log.info("Status from CRM callback for service request id : {} is {}",
                serviceRequest.getId(), eventData.getStatusCode());

        ServiceRequestStatus updateStatus = CrmEventUtil.statusCodeToRequestStatus(eventData.getStatusCode(),
                eventData.getSolutionOutcomeCode());

        log.info("Updating service request status with id : {} to status : {} from CRM callback",
                serviceRequest.getId(), updateStatus);

        int updateResult = serviceRequestRepository.updateServiceRequestStatusByEvent(
                serviceRequest.getId(),
                updateStatus,
                event.getEventId(),
                event.getEventTimestamp(),
                event.getSourceSystem()
        );


        if (updateResult == 0) {
            log.warn("Ignored staled event (id: {}, timestamp: {}, recordIdentifier: {})",
                    event.getEventId(), event.getEventTimestamp(), event.getRecordIdentifier());
            return;
        }

        // Handle actions based on update status CONFIRMED
        if (updateStatus.equals(ServiceRequestStatus.IN_PROGRESS) && Objects.isNull(serviceRequest.getCrmSrNumber())) {
            serviceRequestRepository.updateServiceRequestCrmSrNumber(
                    serviceRequest.getId(),
                    eventData.getSrNumber()
            );
            log.info("Service request (id: {}) updated with CRM SR number and sending notification: {}",
                serviceRequest.getId(), eventData.getSrNumber());
            notificationHelper.sendRequestConfirmationSmsToUnitManager(serviceRequest, eventData.getSrNumber());
        }

        log.info("Update Service request by event (id: {}, timestamp: {}, recordIdentifier: {}) successfully",
                event.getEventId(), event.getEventTimestamp(), event.getRecordIdentifier());

        notificationHelper.handlePublishNotificationOnRequestState(serviceRequest, updateStatus);
    }

}
