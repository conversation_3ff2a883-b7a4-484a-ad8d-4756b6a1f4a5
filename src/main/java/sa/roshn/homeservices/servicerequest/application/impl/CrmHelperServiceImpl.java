package sa.roshn.homeservices.servicerequest.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.storage.Blob;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import sa.roshn.artifacts._share_.log.RoshnModule;
import sa.roshn.artifacts.auth.forgerock.dto.response.UserIdentityResponse;
import sa.roshn.artifacts.auth.forgerock.services.ForgeRockService;
import sa.roshn.artifacts.order.shopboxo.application.ShopboxoHelperService;
import sa.roshn.artifacts.order.shopboxo.infrastructure.model.GetProductDetailsByIdResponse;
import sa.roshn.homeservices._shared._kernel.application.CrmLookupService;
import sa.roshn.homeservices._shared._kernel.application.CrmPushHelper;
import sa.roshn.homeservices._shared._kernel.domain.model.PushEvent;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.client.CrmEventFeignClient;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CreateCrmServiceRequestResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmChannelTypeCode;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmEmailChannel;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmEventData;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmEventType;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmPullEvent;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmPushEventResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmPushEventResult;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmRescheduleServiceResponse;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmServiceRequestOutcome;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmServiceRequestStatusCode;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmServiceRequestType;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmSeverityCode;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.CrmSourceSystem;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetCommunitiesResponse.CommunityInfo;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.GetProductsResponse.ProductInfo;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.entity.CrmProductMapping;
import sa.roshn.homeservices._shared._kernel.infrastructure.crm.model.repository.CrmProductMappingRepository;
import sa.roshn.homeservices._shared._kernel.util.CrmConverterUtil;
import sa.roshn.homeservices._shared.file.CrmMultipartFile;
import sa.roshn.homeservices._shared.http.error.CommonHttpError;
import sa.roshn.homeservices._shared.http.error.RoshnHttpException;
import sa.roshn.homeservices._shared.log.ErrorSource;
import sa.roshn.homeservices._shared.log.SystemErrorCode;
import sa.roshn.homeservices._shared.log.SystemType;
import sa.roshn.homeservices._shared.util.EnvironmentUtil;
import sa.roshn.homeservices._shared.util.InstantUtil;
import sa.roshn.homeservices._shared.util.MultipartFormDataParser;
import sa.roshn.homeservices.servicerequest.application.CrmHelperService;
import sa.roshn.homeservices.servicerequest.application.ServiceRequestAssetService;
import sa.roshn.homeservices.servicerequest.constant.CrmConstant;
import sa.roshn.homeservices.servicerequest.domain.entity.ServiceRequestAsset;
import sa.roshn.homeservices.servicerequest.domain.entity.ServiceRequestEntity;
import sa.roshn.homeservices.servicerequest.domain.error.ServiceRequestErrorCode;
import sa.roshn.homeservices.servicerequest.domain.error.ServiceRequestHttpError;
import sa.roshn.homeservices.servicerequest.util.ServiceRequestActionLogHelper;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static sa.roshn.homeservices.servicerequest.constant.CrmConstant.OBJECT_NAME;
import static sa.roshn.homeservices.servicerequest.constant.CrmConstant.SOURCE_SYSTEM;
import static sa.roshn.homeservices.servicerequest.util.CrmEventUtil.buildCrmMultipartFileFromBlob;

@Service
@Slf4j
@RequiredArgsConstructor
@RoshnModule(properties = {
    @RoshnModule.Property(key = "name", value = "service-request")
})
public class CrmHelperServiceImpl implements CrmHelperService {
    private final CrmEventFeignClient crmEventFeignClient;
    private final ForgeRockService forgeRockService;
    private final ObjectMapper objectMapper;
    private final ShopboxoHelperService shopboxoHelperService;
    private final CrmLookupService crmLookupService;
    private final ServiceRequestAssetService serviceRequestAssetService;
    private final CrmProductMappingRepository crmProductMappingRepository;
    private final CrmPushHelper crmPushHelper;
    private final ServiceRequestActionLogHelper serviceRequestActionLogHelper;

    public static final Map<String, String> SHOPBOXO_CATEGORY_TO_CRM_CATEGORY_MAP;

    public static final String CRM_FALLBACK_CODE = "OTHERS";

    static {
        SHOPBOXO_CATEGORY_TO_CRM_CATEGORY_MAP = Map.of(
                "ac-maintenance", "AC_IS_NOT_WORKING-ORA_SVC_CRM-300000001821681",
                "sub-carpentry", "ARCHITECTURE_DESIGN_OTHERS-ORA_SVC_CRM-300000001821681",
                "subcarpentry", "ARCHITECTURE_DESIGN_OTHERS-ORA_SVC_CRM-300000001821681",
                "sub-eletric", "CM_ELECTRICAL-ORA_SVC_CRM-300000001821681",
                "subelectric", "CM_ELECTRICAL-ORA_SVC_CRM-300000001821681",
                "plumbing-2", "WATER_PLUMBING-ORA_SVC_CRM-300000001821681",
                "subplumbing", "WATER_PLUMBING-ORA_SVC_CRM-300000001821681",
                "submit-a-general-request", "OTHER_SERVICES"
        );
    }

    @Override
    public CrmRescheduleServiceResponse rescheduleServiceRequest(
            UserIdentityResponse user,
            ServiceRequestEntity serviceRequest,
            Instant newAppointmentDate,
            String shopboxoProductId) {
        try {
            var currentEventData = this.constructCrmEventData(user, serviceRequest, shopboxoProductId);
            /* Problem description is in this format:
             * Appointment Date: 2021-09-30T10:00:00.000Z. HomeService Category Title: Category - Product Name...
             * Get appointment date from this and replace it with new appointment date
             */

            String serializedNewEventData = objectMapper.writeValueAsString(currentEventData);
            var eventTimestamp = Instant.now();
            var eventTimestampStr = InstantUtil.toZTimestampString(eventTimestamp);
            var pushEventResponse = crmEventFeignClient.pushServiceRequestEvent(
                    CrmEventType.Update.name(),
                    SOURCE_SYSTEM,
                    OBJECT_NAME,
                    serviceRequest.getCrmRecordIdentifier(),
                    eventTimestampStr,
                    serializedNewEventData
            );
            var recordIdentifier = currentEventData.getHomeServicesSRNo();
            return CrmRescheduleServiceResponse.builder()
                    .eventId(pushEventResponse.getEventId())
                    .recordIdentifier(recordIdentifier)
                    .eventTimestamp(eventTimestamp)
                    .build();
        } catch (Exception ex) {
            serviceRequestActionLogHelper.logError(
                ServiceRequestErrorCode.RESCHEDULING_ERROR,
                ErrorSource.CRM,
                SystemType.EXTERNAL.getName(),
                "Error while rescheduling service request on CRM",
                ex
            );
            throw new RoshnHttpException(CommonHttpError.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public CreateCrmServiceRequestResponse createCrmServiceRequest(
            ServiceRequestEntity serviceRequest,
            String shopboxoProductId) {
        try {
            // Get user from ForgeRock user id
            Optional<UserIdentityResponse> userOptional = forgeRockService
                    .getUserByForgeRockId(serviceRequest.getForgeRockUserId());
            if (userOptional.isEmpty()) {
                throw new RoshnHttpException(ServiceRequestHttpError.CRM_USER_NOT_FOUND);
            }
            UserIdentityResponse user = userOptional.get();

            // Get CRM unit info
            CrmEventData crmEventData = this.constructCrmEventData(user, serviceRequest, shopboxoProductId);
            String serializedEventData = objectMapper.writeValueAsString(crmEventData);

            var eventTimestamp = Instant.now();
            var recordIdentifier = this.buildRecordIdentifier(serviceRequest.getId().toString());

            PushEvent event = PushEvent.builder()
                    .eventType(CrmEventType.Create)
                    .sourceSystem(CrmSourceSystem.HOME_SERVICES)
                    .objectName(OBJECT_NAME)
                    .recordIdentifier(recordIdentifier)
                    .eventTimestamp(eventTimestamp)
                    .crmEventData(serializedEventData)
                    .build();
            log.info("Creating service request on CRM for record id: {}, Event Data: {}",
                    recordIdentifier, event.getCrmEventData());
            CrmPushEventResponse response = crmPushHelper.pushEvent(event);

            return CreateCrmServiceRequestResponse.builder()
                    .eventId(response.getEventId())
                    .recordIdentifier(recordIdentifier)
                    .eventTimestamp(eventTimestamp)
                    .build();
        } catch (Exception ex) {
            serviceRequestActionLogHelper.logError(
                SystemErrorCode.CRM_REQUEST_ERROR,
                ErrorSource.CRM,
                SystemType.EXTERNAL.getName(),
                "Error while creating service request on CRM",
                ex
            );
            throw new RoshnHttpException(CommonHttpError.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public void uploadServiceRequestImage(ServiceRequestAsset asset) {
        try {
            var eventTimestamp = Instant.now();
            Blob attachmentBlob = serviceRequestAssetService.getObjectFromAsset(asset.getServiceRequestId(), asset);
            CrmMultipartFile file = buildCrmMultipartFileFromBlob(attachmentBlob);
            List<MultipartFile> files = List.of(file);
            log.info("Uploading images to CRM for asset {} ...", asset.getId());
            var recordIdentifier = this.buildRecordIdentifier(String.valueOf(asset.getServiceRequestId()));
            PushEvent event = PushEvent.builder()
                    .eventType(CrmEventType.Update)
                    .sourceSystem(CrmSourceSystem.HOME_SERVICES)
                    .objectName(OBJECT_NAME)
                    .recordIdentifier(recordIdentifier)
                    .eventTimestamp(eventTimestamp)
                    .crmAttachments(files)
                    .build();
            var response = crmPushHelper.pushEventImage(event);
            log.info("Images uploaded for record id {}, timestamp {} and event id {}",
                    recordIdentifier, eventTimestamp, response.getEventId());
        } catch (Exception ex) {
            serviceRequestActionLogHelper.logError(
                ServiceRequestErrorCode.ASSET_UPLOAD_ERROR,
                ErrorSource.CRM,
                SystemType.EXTERNAL.getName(),
                "Error while uploading images to CRM for asset {}",
                ex,
                asset.getId()
            );
            throw new RoshnHttpException(CommonHttpError.INTERNAL_SERVER_ERROR);
        }
    }

    private CrmProductMapping getCRMProductMapping(String shopboxoProductSlug) {
        var fallbackMapping = CrmProductMapping.builder()
                .crmProductCode(CRM_FALLBACK_CODE)
                .severityCode(CrmSeverityCode.MEDIUM.getValue())
                .build();
        if (Objects.isNull(shopboxoProductSlug)) {
            return fallbackMapping;
        }
        Optional<CrmProductMapping> crmProductCode = crmProductMappingRepository
                .findByShopboxoProductCode(shopboxoProductSlug);
        return crmProductCode.orElse(fallbackMapping);
    }

    @Override
    public CrmPullEvent getCrmEventByServiceRequest(ServiceRequestEntity serviceRequest) {
        String eventTimestampStr = InstantUtil.toZTimestampString(serviceRequest.getCrmEventTimestamp());

        String currentCrmSourceEvent = Objects.nonNull(serviceRequest.getCrmEventSourceSystem())
                ? serviceRequest.getCrmEventSourceSystem() : CrmSourceSystem.CRM.name();

        Response eventResponse = crmEventFeignClient.pullServiceRequestEvent(
                currentCrmSourceEvent,
                serviceRequest.getCrmEventId(),
                eventTimestampStr,
                "ServiceRequest",
                serviceRequest.getCrmRecordIdentifier(),
                "HOME_SERVICES"
        );
        return parseCrmPullEventResponse(eventResponse);
    }

    /**
     * Parse CRM Pull event response to CRM Pull Event model
     *
     * @param eventResponse Pull Event Response
     * @return CRM Pull Event Model
     */
    private CrmPullEvent parseCrmPullEventResponse(Response eventResponse) {
        List<MultipartFormDataParser.MultipartFormData> formData = MultipartFormDataParser.parse(eventResponse);
        CrmPullEvent pullEvent;
        try {
            pullEvent = CrmConverterUtil.convertPullEventFormDataToModel(formData);
        } catch (Exception ex) {
            serviceRequestActionLogHelper.logError(
                SystemErrorCode.CRM_REQUEST_ERROR,
                ErrorSource.CRM,
                SystemType.EXTERNAL.getName(),
                "Cannot convert pull event from data to model",
                ex
            );
            throw new RoshnHttpException(CommonHttpError.INTERNAL_SERVER_ERROR);
        }
        return pullEvent;
    }

    @Override
    public CrmPushEventResult updateCrmEventStatus(
            UserIdentityResponse user,
            ServiceRequestEntity serviceRequest,
            String shopboxoProductId,
            CrmServiceRequestStatusCode statusCode
    ) {
        CrmEventData crmEventData = this.constructCrmEventData(
                user,
                serviceRequest,
                shopboxoProductId
        );
        crmEventData.setStatusCode(statusCode.getValue()); // Update status

        return pushUpdateEvent(crmEventData);
    }

    @Override
    public CrmPushEventResult updateCrmEventStatus(
            UserIdentityResponse user,
            ServiceRequestEntity serviceRequest,
            String shopboxoProductId,
            CrmServiceRequestStatusCode statusCode,
            CrmServiceRequestOutcome outcome
    ) {
        CrmEventData crmEventData = this.constructCrmEventData(
                user,
                serviceRequest,
                shopboxoProductId
        );
        crmEventData.setStatusCode(statusCode.getValue()); // Update status
        crmEventData.setSolutionOutcomeCode(outcome.getValue());

        return pushUpdateEvent(crmEventData);
    }

    private CrmPushEventResult pushUpdateEvent(CrmEventData crmEventData) {
        String serializedEventData = serializeEventData(crmEventData);
        var eventTimestamp = Instant.now();
        var eventTimestampStr = InstantUtil.toZTimestampString(eventTimestamp);
        // 1:1 to Record Identifier on CRM (with prefix)
        var recordIdentifier = crmEventData.getHomeServicesSRNo();
        var response = crmEventFeignClient.pushServiceRequestEvent(
                CrmEventType.Update.name(),
                CrmSourceSystem.HOME_SERVICES.name(),
                CrmConstant.SERVICE_REQUEST_OBJECT_NAME,
                recordIdentifier,
                eventTimestampStr,
                serializedEventData
        );
        return CrmPushEventResult.builder()
                .eventId(response.getEventId())
                .recordIdentifier(recordIdentifier)
                .eventTimestamp(eventTimestamp)
                .build();
    }

    private String serializeEventData(CrmEventData crmEventData) {
        try {
            return objectMapper.writeValueAsString(crmEventData);
        } catch (JsonProcessingException e) {
            throw new RoshnHttpException(ServiceRequestHttpError.SERIALIZATION_ERROR);
        }
    }

    /**
     * Build record identifier based on active profile to identify environment
     *
     * @param serviceRequestId Service request id
     * @return Formatted record identifier
     */
    private String buildRecordIdentifier(String serviceRequestId) {
        var activeProfile = EnvironmentUtil.getActiveProfile();
        return switch (activeProfile) {
            case "local", "dev" -> String.format("1%s", serviceRequestId);
            case "uat" -> String.format("2%s", serviceRequestId);
            default -> serviceRequestId;
        };
    }

    private CrmEventData buildCrmEventData(UserIdentityResponse user, ProductInfo productInfo,
                                           CommunityInfo community, ServiceRequestEntity serviceRequest,
                                           String problemDescription, String shopboxoProductSlug) {
        CrmProductMapping crmProductMapping = this.getCRMProductMapping(shopboxoProductSlug);
        return CrmEventData.builder()
                .primaryContactRegistryId(user.getRegistryId())
                .severityCode(crmProductMapping.getSeverityCode())
                .statusCode(CrmServiceRequestStatusCode.NEW.getValue())
                .requestType(CrmServiceRequestType.REQUEST.getValue())
                .categoryCode(crmProductMapping.getCrmProductCode())
                .issueArea("UNIT_AREA")
                .community(community.getCommunityName())
                .unitCodeId(productInfo.getUnitCodeId())
                .project(productInfo.getProjectName())
                .city(community.getCommunityCityEN())
                .assetLocation(community.getCommunityName() + "_COMMUNITY")
                .channelTypeCode(CrmChannelTypeCode.ROSHNAPP.getValue())
                .emailChannel(CrmEmailChannel.CUSTOMER_CARE.getValue())
                .problemDescription(problemDescription)
                .homeServicesSRNo(this.buildRecordIdentifier(serviceRequest.getId().toString()))
                .build();
    }

    private CrmEventData constructCrmEventData(
            UserIdentityResponse user,
            ServiceRequestEntity serviceRequest,
            String shopboxoProductId
    ) {
        // Get CRM unit info
        ProductInfo productInfo = crmLookupService.getProductInfoByUnitCode(serviceRequest.getUnitCode());
        List<CommunityInfo> communities = crmLookupService.getCommunites();
        Optional<CommunityInfo> communityOptional = communities.stream()
                .filter(c -> c.getCommunityId().equals(productInfo.getCommunityId()))
                .findFirst();
        if (communityOptional.isEmpty()) {
            throw new RoshnHttpException(ServiceRequestHttpError.CRM_COMMUNITY_NOT_FOUND);
        }
        CommunityInfo community = communityOptional.get();


        GetProductDetailsByIdResponse productDetail = shopboxoHelperService.getProductDetailsById(shopboxoProductId);
        String problemDescription = serviceRequest.getAdditionalDetail();

        return this.buildCrmEventData(
                user,
                productInfo,
                community,
                serviceRequest,
                problemDescription,
                productDetail.getSlug()
        );
    }

}
