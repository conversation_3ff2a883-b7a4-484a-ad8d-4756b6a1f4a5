spring:
  liquibase:
    change-log: classpath:database/migration/db.changelog-master.yml
    enabled: true
  datasource:
    url: jdbc:postgresql://${sm://hs-db-host}:${sm://hs-db-port}/${sm://hs-db-name}
    username: ${sm://hs-db-user}
    password: ${sm://hs-db-password}
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
    open-in-view: false
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 10MB
  data:
    redis:
      host: ${sm://hs-redis-host}
      port: ${sm://hs-redis-port}
  jackson:
    default-property-inclusion: NON_NULL
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://iam-dev.myroshn.com:443/am/oauth2/realms/root/realms/roshn
          jwk-set-uri: https://iam-dev.myroshn.com:443/am/oauth2/roshn/connect/jwk_uri
          audiences: RoshnSA
  task:
    scheduling:
      pool:
        size: 10
  autoconfigure:
    exclude: ''
  allowed-origins: "*"
  cloud:
    openfeign:
      okhttp:
        enabled: true
      client:
        config:
          default:
            connectTimeout: 60000
            readTimeout: 120000
            loggerLevel: full
      micrometer:
        enabled: false
    gcp:
      project-id: roshn-com-dev
      secretmanager:
        enabled: true
        project-id: roshn-com-dev
      credentials:
        location: #{null}
      pubsub:
        subscriptions:
          payment-HS: platform-payment-callback-hs-sub
        topics:
          notification: topic-notification
        publisher:
          enable-message-ordering: true
        enabled: true
        project-id: roshn-com-dev
      cloud-tasks:
        queues:
          home-services:
            name: home-services-queue
            location: europe-west1
        secret: ${sm://hs-cloud-tasks-secret}
      logging:
        enabled: true
      trace:
        enabled: true
  config:
    import: sm://
  lifecycle:
    timeout-per-shutdown-phase: 60s
server:
  shutdown: graceful
  public-url: https://be-dev.fb611.com
springdoc:
  override-with-generic-response: false
  api-docs:
    enabled: true
  swagger-ui:
    config-url: /v3/api-docs/swagger-config
    url: /v3/api-docs
    enabled: true
logging:
  config: classpath:logback.xml
  level:
    root: INFO
    sa:
      roshn:
        homeservices:
          _shared:
            _kernel:
              infrastructure:
                crm:
                  client: DEBUG
                platform:
                  client: DEBUG
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: INFO
management:
  metrics:
    tags:
      app: roshn-com-backend-home-services
      instance: ${random.uuid}
  endpoints:
    web:
      exposure:
        include:
          - "*"
  stackdriver:
    metrics:
      export:
        enabled: true
        project-id: roshn-com-dev
        use-semantic-metric-types: true
        batch-size: 200
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: w3c
cors:
  allowed-origins: "*"
swagger:
  open-api:
    version: v1
crm:
  username: ${sm://hs-crm-user}
  password: ${sm://hs-crm-password}
  base-url: https://xxroshndevoic-axp3pwu4ewpn-je.integration.ocp.oraclecloud.com:443/ic/api/integration/v1/flows/rest
  mock-contact-enabled: true
  mock-contact-id: 300000518915716
  mock-contact-registry-id: 730356
  api:
    url: https://api-dev.roshn.sa
    username: ${sm://hs-crm-api-user}
    password: ${sm://hs-crm-api-password}
  ent:
    api:
      url: https://api-ent-dev.roshn.sa
      username: ${sm://hs-crm-ent-api-user}
      password: ${sm://hs-crm-ent-api-password}
forgerock:
  base-url: https://iam-dev.myroshn.com/
  client-id: ${sm://hs-forgerock-client-id}
  client-secret: ${sm://hs-forgerock-client-secret}
  authorization-grant-type: client_credentials
  scope: fr:idm:*
  auth-enabled: true
  apis:
    get-access-token: am/oauth2/roshn/access_token
    get-user-by-id: openidm/managed/user/{userId}
    update-user-by-id: openidm/managed/user/{userId}
    query-user-by-national-id: openidm/managed/user/
togglz:
  enabled: true
  bucket-name: roshn-com-app-dev-feature-flags
  console:
    enabled: true
    secured: true
    path: /togglz-console
system:
  admin:
    username: ${sm://hs-system-admin-username}
    password: ${sm://hs-system-admin-password}
feign-test:
  baseUrl: https://api.github.com
shopboxo:
  baseUrl: https://dev-api.shop.myroshn.com
  webUrl: https://dev-web.shop.myroshn.com
  clientId: ${sm://hs-shopboxo-client-id}
  storeId: r-devteam
  system-token: ${sm://hs-shopboxo-system-key}
  scheduled-booking-enabled: false
storage:
  asset:
    bucket-name: roshn-com-app-dev-homeservices-assets
    max-file-size: 5242880 # 5MB in bytes
    signed-url-ttl: 900 # in seconds
    upload-rate-limit-count: 5
    filename-length: 8
    servicerequest:
      folder-name: service-requests
      quota-per-request: 5
    domain: https://************.nip.io
platform:
  base-url: http://platform-service.backend-platform.svc.cluster.local
strapi:
  base-url: https://************.nip.io/cms/
  bearer-token: ${sm://hs-strapi-token}
  apis:
    get-property-detail-by-unit-code: api/slugify/slugs/property-detail/{unitCode}
dynamic-config:
  enabled: true
  bucket-name: roshn-com-app-dev-dynamic-config
  blob-name: config.yaml
scheduler:
  cron:
    service-action-logger: "*/10 * * * * *"
    crm-api-mapping: "0 0 */6 * * *"
