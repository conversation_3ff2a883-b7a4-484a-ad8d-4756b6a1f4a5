spring:
  liquibase:
    change-log: classpath:database/migration/db.changelog-master.yml
    enabled: true
  datasource:
    url: **********************************************
    username: postgres
    password: roshn
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
    open-in-view: false
  data:
    redis:
      host: test
      port: 3123
  jackson:
    default-property-inclusion: NON_NULL
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://iam-dev.myroshn.com:443/am/oauth2/realms/root/realms/roshn
          jwk-set-uri: https://iam-dev.myroshn.com:443/am/oauth2/roshn/connect/jwk_uri
          audiences: RoshnSA
  task:
    scheduling:
      pool:
        size: 10
  autoconfigure:
    exclude: com.google.cloud.spring.autoconfigure.trace.StackdriverTraceAutoConfiguration
  allowed-origins: "*"
  cloud:
    openfeign:
      okhttp:
        enabled: true
      client:
        config:
          default:
            connectTimeout: 60000
            readTimeout: 60000
            loggerLevel: full
            followRedirects: false
          dummyFeignClient:
            connectTimeout: 3000
            readTimeout: 3000
            loggerLevel: full
          shopboxoFeignClient:
            followRedirects: true
      micrometer:
        enabled: false
    gcp:
      project-id: roshn-com-dev
      secretmanager:
        enabled: false
        project-id: roshn-com-dev
      credentials:
        location: classpath:credential/dev-service-account.json
      pubsub:
        subscriptions:
          payment-HS: platform-payment-callback-hs-sub
        topics:
          notification: topic-notification
        enabled: true
        project-id: roshn-com-dev
  lifecycle:
    timeout-per-shutdown-phase: 60s
server:
  shutdown: graceful
  port: 8080
  public-url: https://busy-ladybird-lately.ngrok-free.app
springdoc:
  override-with-generic-response: false
  api-docs:
    enabled: true
  swagger-ui:
    config-url: /v3/api-docs/swagger-config
    url: /v3/api-docs
    enabled: true
logging:
  level:
    sa:
      roshn:
        homeservices:
          feignclient: DEBUG
    root: debug
  config: classpath:logback-local.xml
management:
  metrics:
    tags:
      app: roshn-com-backend-home-services
      instance: ${random.uuid}
  endpoints:
    web:
      exposure:
        include:
          - "*"
  stackdriver:
    metrics:
      export:
        enabled: false
        project-id: roshn-com-dev
        use-semantic-metric-types: false
        batch-size: 200
  tracing:
    sampling:
      probability: 1.0
    enabled: false
cors:
  allowed-origins: "*"
swagger:
  open-api:
    version: v1
crm:
  username: <EMAIL>
  password: R0S$hnSIT1@2023
  base-url: https://xxroshndevoic-axp3pwu4ewpn-je.integration.ocp.oraclecloud.com:443/ic/api/integration/v1/flows/rest
  api:
    url: https://api-dev.roshn.sa
    username: ${sm://hs-crm-api-user}
    password: ${sm://hs-crm-api-password}
  ent:
    api:
      url: https://api-ent-dev.roshn.sa
      username: ${sm://hs-crm-ent-api-user}
      password: ${sm://hs-crm-ent-api-password}
forgerock:
  base-url: https://iam-dev.myroshn.com/
  client-id: DevRoshnBackend
  client-secret: T7t6FUJcPg
  fe-client-id: RoshnSA
  redirect-uri: https://home-dev.myroshn.com/callback.html
  authorization-grant-type: client_credentials
  scope: fr:idm:*
  auth-enabled: true
  apis:
    get-access-token: am/oauth2/roshn/access_token
    get-user-by-id: openidm/managed/user/{userId}
    update-user-by-id: openidm/managed/user/{userId}
    query-user-by-national-id: openidm/managed/user/
    authenticate: /am/json/realms/root/realms/roshn/authenticate
    authorize: /am/oauth2/realms/root/realms/roshn/authorize
    access-token: /am/oauth2/realms/root/realms/roshn/access_token
    create-new-user: /openidm/managed/user?_action=create
    delete-user-by-id: /openidm/managed/user/{userId}
togglz:
  enabled: false
  bucket-name: roshn-com-app-dev-feature-flags
  console:
    enabled: true
    secured: true
    path: /togglz-console
system:
  admin:
    username: admin
    password: pNo6iVNQ4afkYRE
feign:
  client:
    config:
      default:
        loggerLevel: basic
feign-test:
  baseUrl: http://localhost:9561
shopboxo:
  baseUrl: https://api-staging.shopboxo.io
  clientId: pNzDgjiPj4poyTydfBpr4C2Yf6LtbtQZNxv7awU7
  storeId: r-devteam
  scheduled-booking-enabled: false
storage:
  asset:
    bucket-name: roshn-com-app-dev-homeservices-assets
    servicerequest:
      folder-name: service-requests
      quota-per-request: 4
    domain: https://************.nip.io
platform:
  base-url: http://platform-service.backend-platform.svc.cluster.local
