spring:
  liquibase:
    change-log: classpath:database/migration/db.changelog-master.yml
    enabled: true
  datasource:
    url: jdbc:postgresql://${sm://hs-db-host}:${sm://hs-db-port}/${sm://hs-db-name}
    username: ${sm://hs-db-user}
    password: ${sm://hs-db-password}
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
    open-in-view: false
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 10MB
  data:
    redis:
      host: ${sm://hs-redis-host}
      port: ${sm://hs-redis-port}
  jackson:
    default-property-inclusion: NON_NULL
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://iam.roshn.sa:443/am/oauth2/realms/root/realms/roshn
          jwk-set-uri: https://iam.roshn.sa:443/am/oauth2/roshn/connect/jwk_uri
          audiences: RoshnSA
  task:
    scheduling:
      pool:
        size: 10
  autoconfigure:
    exclude: ''
  allowed-origins: "*"
  cloud:
    openfeign:
      okhttp:
        enabled: true
      client:
        config:
          default:
            connectTimeout: 60000
            readTimeout: 120000
            loggerLevel: full
      micrometer:
        enabled: false
    gcp:
      project-id: roshn-com-prod
      secretmanager:
        enabled: true
        project-id: roshn-com-prod
      credentials:
        location: #{null}
      pubsub:
        subscriptions:
          payment-HS: platform-payment-callback-hs-sub
        topics:
          notification: topic-notification
        publisher:
          enable-message-ordering: true
        enabled: true
        project-id: roshn-com-prod
      cloud-tasks:
        queues:
          home-services:
            name: backend-home-services-queue
            location: europe-west1
        secret: ${sm://hs-cloud-tasks-secret}
      logging:
        enabled: true
      trace:
        enabled: true
  config:
    import: sm://
  lifecycle:
    timeout-per-shutdown-phase: 60s
server:
  shutdown: graceful
  public-url: https://alb-home.roshn.sa/home-services
shopboxo:
  baseUrl: 'https://api.shop.roshn.sa'
  webUrl: 'https://web.shop.roshn.sa'
  clientId: ${sm://hs-shopboxo-client-id}
  storeId: rstore
  system-token: ${sm://hs-shopboxo-system-key}
  scheduled-booking-enabled: false
springdoc:
  override-with-generic-response: false
  api-docs:
    enabled: false
  swagger-ui:
    config-url: /v3/api-docs/swagger-config
    url: /v3/api-docs
    enabled: false
logging:
  config: classpath:logback.xml
  level:
    root: INFO
    sa:
      roshn:
        homeservices:
          _shared:
            _kernel:
              infrastructure:
                crm:
                  client: DEBUG
                platform:
                  client: DEBUG
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: INFO
management:
  metrics:
    tags:
      app: roshn-com-backend-home-services
      instance: ${random.uuid}
  endpoints:
    web:
      exposure:
        include:
          - "*"
  stackdriver:
    metrics:
      export:
        enabled: true
        project-id: roshn-com-prod
        use-semantic-metric-types: true
        batch-size: 200
  tracing:
    sampling:
      probability: 1.0
    enabled: true
    propagation:
      type: w3c
swagger:
  open-api:
    version: v1
cors:
  allowed-origins: "*"
crm:
  username: ${sm://hs-crm-user}
  password: ${sm://hs-crm-password}
  base-url: https://app.roshn.sa/ic/api/integration/v1/flows/rest
  mock-contact-enabled: true
  mock-contact-id: 300000515383835
  mock-contact-registry-id: 723560
  api:
    url: https://api-prod.roshn.sa
    username: ${sm://hs-crm-api-user}
    password: ${sm://hs-crm-api-password}
  ent:
    api:
      url: https://api-ent.roshn.sa
      username: ${sm://hs-crm-ent-api-user}
      password: ${sm://hs-crm-ent-api-password}
forgerock:
  base-url: https://iam.roshn.sa/
  client-id: ${sm://hs-forgerock-client-id}
  client-secret: ${sm://hs-forgerock-client-secret}
  authorization-grant-type: client_credentials
  fe-client-id: #{null}
  redirect-uri: #{null}
  scope: fr:idm:*
  auth-enabled: true
  apis:
    get-access-token: am/oauth2/roshn/access_token
    get-user-by-id: openidm/managed/user/{userId}
    update-user-by-id: openidm/managed/user/{userId}
    query-user-by-national-id: openidm/managed/user/
togglz:
  enabled: true
  bucket-name: roshn-com-app-prod-feature-flags
  console:
    enabled: true
    secured: true
    path: /togglz-console
system:
  admin:
    username: ${sm://hs-system-admin-username}
    password: ${sm://hs-system-admin-password}
storage:
  asset:
    bucket-name: roshn-com-app-prod-homeservices-assets
    max-file-size: 5242880 # 5MB in bytes
    signed-url-ttl: 900 # in seconds
    upload-rate-limit-count: 5
    filename-length: 8
    servicerequest:
      folder-name: service-requests
      quota-per-request: 5
    domain: https://alb-home.roshn.sa
platform:
  base-url: http://platform-service.backend-platform.svc.cluster.local
strapi:
  base-url: https://************.nip.io/cms/
  bearer-token: 59b1cec7045a1b39bf56482567325933e07bd2625d7c55bb63ceb3bcc37ef9c36e050d72d0cd705b7c2e6578391b2d07f1c13ef2cf90035fd1ed4f1f51d8b96fa5a8db55abf87783103de39a55ed5f69fcaf9cf70ed3e97440a44246ea8ee3e7f9fdc647e058f17b6fdd21d87c0e02020bc04f97b09deb00d41ebae769983c23
  apis:
    get-property-detail-by-unit-code: api/slugify/slugs/property-detail/{unitCode}

dynamic-config:
  enabled: true
  bucket-name: prod-cloud-storage-bucket-dynamic-config
  blob-name: config.yaml
scheduler:
  cron:
    service-action-logger: "*/10 * * * * *"
    crm-api-mapping: "0 0 */6 * * *"
