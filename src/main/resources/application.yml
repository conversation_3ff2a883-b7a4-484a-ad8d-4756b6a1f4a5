spring:
  profiles:
    active: local
  liquibase:
    change-log: classpath:database/migration/db.changelog-master.yml
    enabled: true
  datasource:
    url: *****************************************
    username: postgres
    password: roshn
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
    open-in-view: false
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 10MB
  data:
    redis:
      host: localhost
      port: 6379
  jackson:
    default-property-inclusion: NON_NULL
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://iam-dev.myroshn.com:443/am/oauth2/realms/root/realms/roshn
          jwk-set-uri: https://iam-dev.myroshn.com:443/am/oauth2/roshn/connect/jwk_uri
          audiences: RoshnSA
  task:
    scheduling:
      pool:
        size: 10
  autoconfigure:
    exclude: com.google.cloud.spring.autoconfigure.trace.StackdriverTraceAutoConfiguration
  cloud:
    openfeign:
      okhttp:
        enabled: true
      client:
        config:
          default:
            connectTimeout: 60000
            readTimeout: 120000
            loggerLevel: full
          dummyFeignClient:
            connectTimeout: 3000
            readTimeout: 3000
            loggerLevel: full
      micrometer:
        enabled: false
    gcp:
      project-id: roshn-com-dev
      secretmanager:
        enabled: false
        project-id: roshn-com-dev
      credentials:
        location: classpath:credential/dev-service-account.json
      pubsub:
        subscriptions:
          payment-HS: platform-payment-callback-hs-sub
        topics:
          notification: topic-notification
        publisher:
          enable-message-ordering: true
        enabled: true
        project-id: roshn-com-dev
      cloud-tasks:
        queues:
          home-services:
            name: home-services-queue
            location: europe-west1
        secret: KumN9pvCuZlf1I0awjuFPYBIz9bZkypT # 32 chars
      logging:
        enabled: true
      trace:
        enabled: true
  lifecycle:
    timeout-per-shutdown-phase: 60s
server:
  shutdown: graceful
  port: 8080
  public-url: https://busy-ladybird-lately.ngrok-free.app # Change to your NGROK's host url if testing
springdoc:
  override-with-generic-response: false
  api-docs:
    enabled: true
  swagger-ui:
    config-url: /v3/api-docs/swagger-config
    url: /v3/api-docs
    enabled: true
logging:
  config: classpath:logback-local.xml
  level:
    root: INFO
    sa:
      roshn:
        homeservices:
          _shared:
            _kernel:
              infrastructure:
                crm:
                  client: DEBUG
                platform:
                  client: DEBUG

    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: DEBUG
        cache:
management:
  metrics:
    tags:
      app: roshn-com-backend-home-services
      instance: ${random.uuid}
  endpoints:
    web:
      exposure:
        include:
          - "*"
  stackdriver:
    metrics:
      export:
        enabled: false
        project-id: roshn-com-dev
        use-semantic-metric-types: true
        batch-size: 50
  tracing:
    sampling:
      probability: 1.0
    enabled: false
cors:
  allowed-origins: "*"
swagger:
  open-api:
    version: v1
crm:
  username: <EMAIL>
  password: R0S$hnSIT1@2023
  base-url: https://xxroshndevoic-axp3pwu4ewpn-je.integration.ocp.oraclecloud.com:443/ic/api/integration/v1/flows/rest
  mock-contact-enabled: true
  mock-contact-id: 300000518915716
  mock-contact-registry-id: 730356
  api:
    url: https://api-dev.roshn.sa
    username: ${sm://hs-crm-api-user}
    password: ${sm://hs-crm-api-password}
  ent:
    api:
      url: https://api-ent-dev.roshn.sa
      username: ${sm://hs-crm-ent-api-user}
      password: ${sm://hs-crm-ent-api-password}
forgerock:
  base-url: https://iam-dev.myroshn.com/
  client-id: DevRoshnBackend
  client-secret: T7t6FUJcPg
  authorization-grant-type: client_credentials
  fe-client-id: RoshnSA
  redirect-uri: https://roshn-com-app-dev-fe-web.web.app/callback.html
  scope: fr:idm:*
  auth-enabled: true
  apis:
    get-access-token: am/oauth2/roshn/access_token
    get-user-by-id: openidm/managed/user/{userId}
    update-user-by-id: openidm/managed/user/{userId}
    query-user-by-national-id: openidm/managed/user/
togglz:
  enabled: true
  bucket-name: roshn-com-app-dev-feature-flags
  console:
    enabled: true
    secured: true
    path: /togglz-console
system:
  admin:
    username: admin
    password: pNo6iVNQ4afkYRE
feign-test:
    baseUrl: https://api.github.com
shopboxo:
  baseUrl: https://api-staging.shopboxo.io
  webUrl: https://web-staging.shopboxo.io
  clientId: pNzDgjiPj4poyTydfBpr4C2Yf6LtbtQZNxv7awU7
  storeId: r-devteam
  system-token: 88a99a915e5e9da26dd55f9c1b982716b9b95acc
  scheduled-booking-enabled: false
storage:
  asset:
    filename-length: 8
    max-file-size: 5242880 # in bytes
    signed-url-ttl: 900 # in seconds
    bucket-name: roshn-com-app-dev-homeservices-assets
    upload-rate-limit-count: 5
    servicerequest:
      folder-name: service-requests
      quota-per-request: 5
    domain: https://************.nip.io
platform:
  base-url: http://localhost:8137
strapi:
  base-url: https://************.nip.io/cms/
  bearer-token: 59b1cec7045a1b39bf56482567325933e07bd2625d7c55bb63ceb3bcc37ef9c36e050d72d0cd705b7c2e6578391b2d07f1c13ef2cf90035fd1ed4f1f51d8b96fa5a8db55abf87783103de39a55ed5f69fcaf9cf70ed3e97440a44246ea8ee3e7f9fdc647e058f17b6fdd21d87c0e02020bc04f97b09deb00d41ebae769983c23
  apis:
    get-property-detail-by-unit-code: api/slugify/slugs/property-detail/{unitCode}
dynamic-config:
  enabled: true
  bucket-name: roshn-com-app-dev-dynamic-config
  blob-name: config.yaml
scheduler:
  cron:
    service-action-logger: "*/10 * * * * *"
    crm-api-mapping: "0 0 */6 * * *"
