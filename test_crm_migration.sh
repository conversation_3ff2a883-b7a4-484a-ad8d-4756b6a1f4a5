#!/bin/bash

# CRM API Migration Test Script
# This script tests the CRM API migration to verify everything is working correctly

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080"
CRM_ENT_API_URL="https://api-ent-dev.roshn.sa"
TEST_REGISTRY_ID="730356"
TEST_UNIT_CODE="TEST-UNIT-001"

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ((PASSED_TESTS++))
            ;;
        "FAIL")
            echo -e "${RED}[FAIL]${NC} $message"
            ((FAILED_TESTS++))
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} $message"
            ;;
    esac
    ((TOTAL_TESTS++))
}

# Function to test HTTP endpoint
test_endpoint() {
    local name=$1
    local url=$2
    local expected_status=$3
    local headers=$4
    
    print_status "INFO" "Testing: $name"
    
    if [ -n "$headers" ]; then
        response=$(curl -s -w "%{http_code}" -H "$headers" "$url" || echo "000")
    else
        response=$(curl -s -w "%{http_code}" "$url" || echo "000")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "SUCCESS" "$name - Status: $status_code"
    else
        print_status "FAIL" "$name - Expected: $expected_status, Got: $status_code"
    fi
}

# Function to check if application is running
check_application() {
    print_status "INFO" "Checking if application is running..."
    
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        print_status "SUCCESS" "Application is running at $BASE_URL"
        return 0
    else
        print_status "FAIL" "Application is not accessible at $BASE_URL"
        return 1
    fi
}

# Function to check compilation
check_compilation() {
    print_status "INFO" "Checking if application compiles..."
    
    if ./gradlew compileJava > /dev/null 2>&1; then
        print_status "SUCCESS" "Application compiles successfully"
        return 0
    else
        print_status "FAIL" "Application compilation failed"
        print_status "INFO" "Running compilation to see errors..."
        ./gradlew compileJava
        return 1
    fi
}

# Function to get CRM token (if credentials are available)
get_crm_token() {
    print_status "INFO" "Attempting to get CRM authentication token..."
    
    # Try to get token from CRM Enterprise API
    # Note: This requires valid credentials to be configured
    local token_response=$(curl -s -w "%{http_code}" \
        -X GET "$CRM_ENT_API_URL/v1/oauth/generate?grant_type=client_credentials" \
        -H "Authorization: Basic $(echo -n 'user:pass' | base64)" 2>/dev/null || echo "000")
    
    local status_code="${token_response: -3}"
    
    if [ "$status_code" = "200" ]; then
        print_status "SUCCESS" "CRM token endpoint is accessible"
        # Extract token if needed (simplified for this test)
        return 0
    elif [ "$status_code" = "401" ]; then
        print_status "WARN" "CRM token endpoint accessible but credentials needed"
        return 0
    else
        print_status "WARN" "CRM token endpoint test skipped (status: $status_code)"
        return 0
    fi
}

# Function to test CRM endpoints directly
test_crm_endpoints() {
    print_status "INFO" "Testing CRM Enterprise API endpoints..."
    
    # Test communities endpoint
    test_endpoint "CRM Communities" \
        "$CRM_ENT_API_URL/v1/crm/property-master/communities" \
        "401" \
        "Authorization: Bearer test-token"
    
    # Test products endpoint
    test_endpoint "CRM Products" \
        "$CRM_ENT_API_URL/v1/crm/products?CommunityName=Test&ProjectName=Test&NeighbourhoodName=Test&UnitCode=$TEST_UNIT_CODE" \
        "401" \
        "Authorization: Bearer test-token"
    
    # Test contact exists endpoint
    test_endpoint "CRM Contact Exists" \
        "$CRM_ENT_API_URL/v1/crm/contacts/exists" \
        "401" \
        "Authorization: Bearer test-token"
    
    # Test home owners endpoint
    test_endpoint "CRM Home Owners" \
        "$CRM_ENT_API_URL/v1/crm/homeOwners?RegistryId=$TEST_REGISTRY_ID" \
        "401" \
        "Authorization: Bearer test-token"
    
    # Test events endpoint
    test_endpoint "CRM Events" \
        "$CRM_ENT_API_URL/v1/eventsService/events?SourceSystem=HOME_SERVICES&EventId=test&EventTimestamp=2024-01-01T10:00:00Z&ObjectName=ServiceRequest&RecordIdentifier=test&pullingSystem=HOME_SERVICES" \
        "401" \
        "Authorization: Bearer test-token"
}

# Function to test application endpoints
test_application_endpoints() {
    print_status "INFO" "Testing application endpoints..."
    
    # Test health endpoint
    test_endpoint "Health Check" \
        "$BASE_URL/health" \
        "200"
    
    # Test version endpoint
    test_endpoint "Version Check" \
        "$BASE_URL/version" \
        "200"
    
    # Test unit handover endpoint (without auth - should return 401)
    test_endpoint "Unit Handover (No Auth)" \
        "$BASE_URL/api/v1/unit-handover-date?UnitCode=$TEST_UNIT_CODE" \
        "401"
}

# Function to check application logs for CRM integration
check_logs() {
    print_status "INFO" "Checking application logs for CRM integration..."
    
    # Check if application is using new CRM endpoints
    if [ -f "application.log" ]; then
        if grep -q "api-ent-dev.roshn.sa" application.log; then
            print_status "SUCCESS" "Application logs show new CRM API usage"
        else
            print_status "WARN" "No evidence of new CRM API usage in logs"
        fi
        
        if grep -q "Bearer" application.log; then
            print_status "SUCCESS" "Application logs show Bearer token usage"
        else
            print_status "WARN" "No evidence of Bearer token usage in logs"
        fi
    else
        print_status "WARN" "Application log file not found"
    fi
}

# Function to verify configuration
check_configuration() {
    print_status "INFO" "Checking CRM configuration..."
    
    # Check if configuration files have the new settings
    if grep -q "url: https://api-ent-dev.roshn.sa" src/main/resources/application*.yml; then
        print_status "SUCCESS" "CRM Enterprise API URL configured"
    else
        print_status "FAIL" "CRM Enterprise API URL not found in configuration"
    fi
    
    # Check if Bearer token interceptor exists
    if [ -f "src/main/java/sa/roshn/homeservices/_shared/_kernel/infrastructure/crm/config/CrmBearerTokenInterceptor.java" ]; then
        print_status "SUCCESS" "Bearer token interceptor exists"
    else
        print_status "FAIL" "Bearer token interceptor not found"
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "INFO" "Running integration tests..."
    
    # Run specific tests related to CRM
    if ./gradlew test --tests "*Crm*" > /dev/null 2>&1; then
        print_status "SUCCESS" "CRM-related tests passed"
    else
        print_status "WARN" "Some CRM-related tests may have failed (check test output)"
    fi
}

# Main execution
main() {
    echo "=================================================="
    echo "🚀 CRM API Migration Test Script"
    echo "=================================================="
    echo ""
    
    # Step 1: Check compilation
    if ! check_compilation; then
        print_status "FAIL" "Cannot proceed - compilation failed"
        exit 1
    fi
    
    echo ""
    
    # Step 2: Check configuration
    check_configuration
    echo ""
    
    # Step 3: Check if application is running
    if check_application; then
        echo ""
        
        # Step 4: Test application endpoints
        test_application_endpoints
        echo ""
        
        # Step 5: Check logs
        check_logs
        echo ""
    else
        print_status "WARN" "Application not running - skipping runtime tests"
        echo ""
    fi
    
    # Step 6: Test CRM endpoints directly
    test_crm_endpoints
    echo ""
    
    # Step 7: Get CRM token
    get_crm_token
    echo ""
    
    # Step 8: Run integration tests
    run_integration_tests
    echo ""
    
    # Summary
    echo "=================================================="
    echo "📊 Test Summary"
    echo "=================================================="
    echo "Total Tests: $TOTAL_TESTS"
    echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "${GREEN}✅ All tests passed! CRM migration appears successful.${NC}"
        exit 0
    else
        echo -e "${RED}❌ Some tests failed. Please review the output above.${NC}"
        exit 1
    fi
}

# Help function
show_help() {
    echo "CRM API Migration Test Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -u, --url      Set base URL (default: http://localhost:8080)"
    echo "  -c, --crm-url  Set CRM API URL (default: https://api-ent-dev.roshn.sa)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run with default settings"
    echo "  $0 -u http://localhost:9090          # Run with custom base URL"
    echo "  $0 -c https://api-ent-uat.roshn.sa   # Run with UAT CRM URL"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -c|--crm-url)
            CRM_ENT_API_URL="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
