{"info": {"_postman_id": "crm-api-migration-test", "name": "CRM API Migration Test Collection", "description": "Test collection to verify CRM API migration from old Oracle Integration Cloud endpoints to new Enterprise API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8080", "description": "Base URL for the Home Services application"}, {"key": "crm_ent_api_url", "value": "https://api-ent-dev.roshn.sa", "description": "CRM Enterprise API base URL"}, {"key": "auth_token", "value": "", "description": "Bearer token for authentication"}, {"key": "user_id", "value": "test-user-id", "description": "Test user ID"}, {"key": "registry_id", "value": "730356", "description": "Test registry ID"}, {"key": "unit_code", "value": "TEST-UNIT-001", "description": "Test unit code"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set common headers", "pm.request.headers.add({", "    key: 'Content-Type',", "    value: 'application/json'", "});", "", "// Log request details", "console.log('Request URL:', pm.request.url.toString());", "console.log('Request Method:', pm.request.method);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test assertions", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test('Response has valid JSON', function () {", "    pm.response.to.have.jsonBody();", "});", "", "// Log response details", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');"]}}], "item": [{"name": "1. Health Check", "item": [{"name": "Application Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the application is running and healthy"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Application is healthy', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Health check response is valid', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('status');", "});"], "type": "text/javascript"}}]}, {"name": "Application Version", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/version", "host": ["{{base_url}}"], "path": ["version"]}, "description": "Get application version information"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Version endpoint is accessible', function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}], "description": "Basic health checks to ensure the application is running"}, {"name": "2. CRM Authentication Test", "item": [{"name": "Test CRM Token Generation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Basic {{crm_basic_auth}}", "description": "Basic auth for CRM token generation"}], "url": {"raw": "{{crm_ent_api_url}}/v1/oauth/generate?grant_type=client_credentials", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "o<PERSON>h", "generate"], "query": [{"key": "grant_type", "value": "client_credentials"}]}, "description": "Test direct CRM token generation to verify authentication works"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('CRM token generation successful', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Token response has access_token', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('access_token');", "    pm.expect(responseJson).to.have.property('expires_in');", "    ", "    // Store token for later use", "    pm.collectionVariables.set('crm_token', responseJson.access_token);", "});"], "type": "text/javascript"}}]}], "description": "Test CRM authentication and token generation"}, {"name": "3. User Authorization Tests", "item": [{"name": "Authorize Homeowner", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/user/homeowner/authorization", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "homeowner", "authorization"]}, "description": "Test homeowner authorization which triggers CRM contact verification"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Homeowner authorization response', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 403]);", "});", "", "// Check if CRM integration is working by looking at response", "if (pm.response.code === 200 || pm.response.code === 201) {", "    pm.test('CRM integration successful', function () {", "        const responseJson = pm.response.json();", "        pm.expect(response<PERSON>son).to.have.property('success');", "    });", "}"], "type": "text/javascript"}}]}], "description": "Test user authorization endpoints that interact with CRM"}, {"name": "4. CRM Lookup Tests", "item": [{"name": "Get Communities (CRM Lookup)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{crm_token}}"}], "url": {"raw": "{{crm_ent_api_url}}/v1/crm/property-master/communities", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "crm", "property-master", "communities"]}, "description": "Test the new CRM communities endpoint directly"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Communities endpoint accessible', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Communities response structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('communities');", "});"], "type": "text/javascript"}}]}, {"name": "Get Products (CRM Lookup)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{crm_token}}"}], "url": {"raw": "{{crm_ent_api_url}}/v1/crm/products?CommunityName=Test&ProjectName=Test&NeighbourhoodName=Test&UnitCode={{unit_code}}", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "crm", "products"], "query": [{"key": "CommunityName", "value": "Test"}, {"key": "ProjectName", "value": "Test"}, {"key": "NeighbourhoodName", "value": "Test"}, {"key": "UnitCode", "value": "{{unit_code}}"}]}, "description": "Test the new CRM products endpoint with query parameters"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Products endpoint accessible', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Products response structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('products');", "    });", "}"], "type": "text/javascript"}}]}], "description": "Test CRM lookup endpoints directly"}, {"name": "5. CRM User Data Tests", "item": [{"name": "Check Contact Exists", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{crm_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"registryId\": \"{{registry_id}}\",\n    \"phoneNumber\": \"+966501234567\"\n}"}, "url": {"raw": "{{crm_ent_api_url}}/v1/crm/contacts/exists", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "crm", "contacts", "exists"]}, "description": "Test the new CRM contact exists endpoint"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Contact exists endpoint accessible', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Contact exists response structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('exists');", "    });", "}"], "type": "text/javascript"}}]}, {"name": "Get Home Owners", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{crm_token}}"}], "url": {"raw": "{{crm_ent_api_url}}/v1/crm/homeOwners?RegistryId={{registry_id}}", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "crm", "homeOwners"], "query": [{"key": "RegistryId", "value": "{{registry_id}}"}]}, "description": "Test the new CRM home owners endpoint"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Home owners endpoint accessible', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Home owners response structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('homeOwners');", "    });", "}"], "type": "text/javascript"}}]}, {"name": "Get Tenants", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{crm_token}}"}], "url": {"raw": "{{crm_ent_api_url}}/v1/crm/tenants?TenantRegistryId={{registry_id}}&UnitCode={{unit_code}}", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "crm", "tenants"], "query": [{"key": "TenantRegistryId", "value": "{{registry_id}}"}, {"key": "UnitCode", "value": "{{unit_code}}"}]}, "description": "Test the new CRM tenants endpoint"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Tenants endpoint accessible', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Tenants response structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(response<PERSON>son).to.have.property('tenants');", "    });", "}"], "type": "text/javascript"}}]}], "description": "Test CRM user data endpoints directly"}, {"name": "6. Service Request Tests", "item": [{"name": "Create Service Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"unitCode\": \"{{unit_code}}\",\n    \"additionalDetail\": \"Test service request for CRM API migration\",\n    \"productId\": \"test-product-id\",\n    \"storeId\": \"test-store\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/service-requests", "host": ["{{base_url}}"], "path": ["api", "v1", "service-requests"]}, "description": "Test service request creation which triggers CRM integration"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Service request creation response', function () {", "    pm.expect(pm.response.code).to.be.oneOf([201, 400, 401, 403]);", "});", "", "if (pm.response.code === 201) {", "    pm.test('Service request created successfully', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "        pm.expect(responseJson.data).to.have.property('id');", "        ", "        // Store service request ID for later tests", "        pm.collectionVariables.set('service_request_id', responseJson.data.id);", "    });", "}"], "type": "text/javascript"}}]}], "description": "Test service request operations that trigger CRM integration"}, {"name": "7. Unit and Tenant Tests", "item": [{"name": "Get Unit Handover Date", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/unit-handover-date?UnitCode={{unit_code}}", "host": ["{{base_url}}"], "path": ["api", "v1", "unit-handover-date"], "query": [{"key": "UnitCode", "value": "{{unit_code}}"}]}, "description": "Test unit handover date endpoint which uses CRM unit handover API"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Unit handover date response', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404, 401]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Unit handover date structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "    });", "}"], "type": "text/javascript"}}]}, {"name": "Get Tenants List", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/user/tenant", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "tenant"]}, "description": "Test tenant list endpoint which uses CRM tenant APIs"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Tenants list response', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401, 403]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Tenants list structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "    });", "}"], "type": "text/javascript"}}]}], "description": "Test endpoints that interact with CRM tenant and unit APIs"}, {"name": "8. Household Members Tests", "item": [{"name": "Get Household Members", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/v1/user/household-members?unitCode={{unit_code}}", "host": ["{{base_url}}"], "path": ["api", "v1", "user", "household-members"], "query": [{"key": "unitCode", "value": "{{unit_code}}"}]}, "description": "Test household members endpoint which uses CRM household members API"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Household members response', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 401, 403]);", "});", "", "if (pm.response.code === 200) {", "    pm.test('Household members structure', function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson).to.have.property('data');", "    });", "}"], "type": "text/javascript"}}]}], "description": "Test household member operations that use CRM APIs"}, {"name": "9. CRM Events Tests", "item": [{"name": "Test CRM Event Pull", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{crm_token}}"}], "url": {"raw": "{{crm_ent_api_url}}/v1/eventsService/events?SourceSystem=HOME_SERVICES&EventId=test-event-123&EventTimestamp=2024-01-01T10:00:00Z&ObjectName=ServiceRequest&RecordIdentifier=test-record-123&pullingSystem=HOME_SERVICES", "host": ["{{crm_ent_api_url}}"], "path": ["v1", "eventsService", "events"], "query": [{"key": "SourceSystem", "value": "HOME_SERVICES"}, {"key": "EventId", "value": "test-event-123"}, {"key": "EventTimestamp", "value": "2024-01-01T10:00:00Z"}, {"key": "ObjectName", "value": "ServiceRequest"}, {"key": "RecordIdentifier", "value": "test-record-123"}, {"key": "pullingSystem", "value": "HOME_SERVICES"}]}, "description": "Test the new CRM events pull endpoint"}, "event": [{"listen": "test", "script": {"exec": ["pm.test('CRM events pull endpoint accessible', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 404, 400]);", "});", "", "// Note: This might return 404 if the event doesn't exist, which is expected"], "type": "text/javascript"}}]}], "description": "Test CRM events service endpoints"}, {"name": "10. Integration Verification", "item": [{"name": "Verify Bearer Token Authentication", "event": [{"listen": "prerequest", "script": {"exec": ["// This test verifies that the application is using Bearer token authentication", "// by checking if requests to CRM endpoints include the Authorization header", "", "pm.test('Bearer token interceptor is working', function () {", "    // This will be verified by checking application logs", "    // or by monitoring network requests", "    console.log('Verifying <PERSON><PERSON> token authentication...');", "    ", "    // Set a flag to indicate this test was run", "    pm.collectionVariables.set('bearer_auth_test_run', 'true');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Dummy request to trigger bearer token verification"}}, {"name": "Verify New API Endpoints", "event": [{"listen": "test", "script": {"exec": ["pm.test('New API endpoints verification', function () {", "    // Verify that the application is using the new API endpoints", "    // This can be confirmed by checking application logs or monitoring", "    ", "    console.log('Verifying new API endpoints are being used...');", "    console.log('Expected base URL: https://api-ent-dev.roshn.sa');", "    console.log('Expected endpoints: /v1/crm/*, /v1/eventsService/events');", "    ", "    pm.expect(true).to.be.true; // Placeholder assertion", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Verification test for new API endpoints"}}], "description": "Integration verification tests to ensure the migration is working correctly"}]}